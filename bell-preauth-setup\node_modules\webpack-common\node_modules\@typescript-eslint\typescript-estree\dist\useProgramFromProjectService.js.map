{"version": 3, "file": "useProgramFromProjectService.js", "sourceRoot": "", "sources": ["../src/useProgramFromProjectService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0OA,oEA8DC;AAxSD,kDAA0B;AAC1B,yCAAsC;AACtC,0DAA6B;AAC7B,0DAA6B;AAC7B,+CAAiC;AAUjC,gFAA6E;AAC7E,wEAAoE;AACpE,oDAAwE;AACxE,4GAA8G;AAE9G,MAAM,kBAAkB,GAAG,GAAG,CAAC;AAE/B,MAAM,GAAG,GAAG,IAAA,eAAK,EACf,kEAAkE,CACnE,CAAC;AAEF,MAAM,qBAAqB,GAAG,IAAI,OAAO,EAAsC,CAAC;AAEhF,MAAM,yBAAyB,GAAG,CAChC,OAAiC,EACjC,mBAA6B,EACvB,EAAE;IACR,MAAM,4BAA4B,GAAG,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IAC9E,IACE,CAAC,mBAAI,CAAC,iBAAiB,CAAC,4BAA4B,EAAE,mBAAmB,CAAC,EAC1E,CAAC;QACD,GAAG,CACD,qDAAqD,EACrD,4BAA4B,EAC5B,mBAAmB,CACpB,CAAC;QACF,OAAO,CAAC,oBAAoB,CAAC;YAC3B,mBAAmB,EAAE,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBACzD,SAAS;gBACT,cAAc,EAAE,KAAK;gBACrB,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ;aACnC,CAAC,CAAC;SACJ,CAAC,CAAC;QACH,qBAAqB,CAAC,GAAG,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;QACxD,GAAG,CAAC,mCAAmC,EAAE,mBAAmB,CAAC,CAAC;IAChE,CAAC;AACH,CAAC,CAAC;AAEF,SAAS,gCAAgC,CACvC,0BAAuC,EACvC,uBAAgC,EAChC,gBAAwB,EACxB,aAA6C,EAC7C,eAAuC;IAEvC,MAAM,MAAM,GAAG,4BAA4B,EAAE,CAAC;IAE9C,GAAG,CAAC,gDAAgD,EAAE,MAAM,CAAC,CAAC;IAE9D,GAAG,CACD,4DAA4D,EAC5D,uBAAuB,EACvB,MAAM,CAAC,cAAc,CACtB,CAAC;IAEF,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;QAC1B,IAAI,uBAAuB,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CACb,GAAG,aAAa,CAAC,QAAQ,gIAAgI,CAC1J,CAAC;QACJ,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,WAAW,GAAG,GAAG,aAAa,CAAC,QAAQ,uCAAuC,CAAC;QAErF,MAAM,aAAa,GAAG,mBAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC3D,MAAM,mBAAmB,GAAG,aAAa,CAAC,mBAAmB,CAAC;QAC9D,IACE,CAAC,sCAA6B,CAAC,GAAG,CAAC,aAAa,CAAC;YACjD,CAAC,mBAAmB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAC5C,CAAC;YACD,MAAM,cAAc,GAAG,GAAG,WAAW,0CAA0C,aAAa,qBAAqB,CAAC;YAClH,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CACb,GAAG,cAAc,8EAA8E,CAChG,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,GAAG,cAAc,wEAAwE,CAC1F,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,GAAG,WAAW,6FAA6F,CAC5G,CAAC;QACJ,CAAC;IACH,CAAC;IAED,yEAAyE;IACzE,wEAAwE;IACxE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;QAC3B,0BAA0B,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QACjD,IACE,0BAA0B,CAAC,IAAI;YAC/B,eAAe,CAAC,mCAAmC,EACnD,CAAC;YACD,MAAM,cAAc,GAAG,EAAE,CAAC;YAC1B,MAAM,YAAY,GAAG,CAAC,GAAG,0BAA0B,CAAC,CAAC,KAAK,CACxD,CAAC,EACD,cAAc,CACf,CAAC;YACF,MAAM,kBAAkB,GACtB,0BAA0B,CAAC,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC;YAExD,MAAM,IAAI,KAAK,CACb,oBAAoB,eAAe,CAAC,mCAAmC,sCAAsC,4EAAuC;;EAE1J,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;EAChD,kBAAkB,CAAC,CAAC,CAAC,UAAU,kBAAkB,eAAe,CAAC,CAAC,CAAC,EAAE;;CAEtE,CACM,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;IAEd,SAAS,cAAc;QACrB,OAAO,eAAe,CAAC,OAAO,CAAC,cAAc,CAC3C,gBAAgB,EAChB,aAAa,CAAC,YAAY;QAC1B,gBAAgB,CAAC,SAAS,EAC1B,aAAa,CAAC,eAAe,CAC9B,CAAC;IACJ,CAAC;IAED,SAAS,4BAA4B;QACnC,GAAG,CAAC,iDAAiD,EAAE,gBAAgB,CAAC,CAAC;QAEzE,IAAI,MAAM,GAAG,cAAc,EAAE,CAAC;QAE9B,oEAAoE;QACpE,4DAA4D;QAC5D,yEAAyE;QACzE,IACE,CAAC,MAAM,CAAC,gBAAgB;YACxB,CAAC,MAAM,CAAC,cAAc;YACtB,CAAC,aAAa,CAAC,SAAS;YACxB,CAAC,uBAAuB;YACxB,WAAW,CAAC,GAAG,EAAE,GAAG,eAAe,CAAC,mBAAmB;gBACrD,kBAAkB,EACpB,CAAC;YACD,GAAG,CAAC,+DAA+D,CAAC,CAAC;YACrE,eAAe,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YACzC,MAAM,GAAG,cAAc,EAAE,CAAC;YAC1B,eAAe,CAAC,mBAAmB,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAC1D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;AACH,CAAC;AAED,SAAS,iCAAiC,CACxC,gBAAwB,EACxB,aAA6C,EAC7C,OAAiC;IAEjC,GAAG,CAAC,gEAAgE,CAAC,CAAC;IAEtE,4EAA4E;IAC5E,yBAAyB;IACzB,+GAA+G;IAC/G,iHAAiH;IACjH,IAAI,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAC5C,GAAG,CAAC,gEAAgE,CAAC,CAAC;QACtE,OAAO,CAAC,cAAc,CACpB,gBAAgB,EAChB,aAAa,CAAC,YAAY;QAC1B,gBAAgB,CAAC,SAAS,EAC1B,aAAa,CAAC,eAAe,CAC9B,CAAC;IACJ,CAAC;IAED,OAAO,IAAA,kCAAe,EAAC,aAAa,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,wBAAwB,CAC/B,gBAAwB,EACxB,aAA6C,EAC7C,eAAuC;IAEvC,GAAG,CAAC,iDAAiD,EAAE,gBAAgB,CAAC,CAAC;IAEzE,MAAM,UAAU,GAAG,eAAe,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAC3E,6DAA6D;IAC7D,MAAM,OAAO,GAAG,eAAe,CAAC,OAAO;SACpC,wBAAwB,CAAC,UAAW,CAAC,QAAQ,EAAE,IAAI,CAAE;SACrD,kBAAkB,CAAC,sBAAsB,CAAC,IAAI,CAAC;SAC/C,UAAU,EAAE,CAAC;IAChB,4DAA4D;IAE5D,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,GAAG,CAAC,gDAAgD,EAAE,gBAAgB,CAAC,CAAC;QACxE,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,GAAG,CAAC,uCAAuC,EAAE,gBAAgB,CAAC,CAAC;IAE/D,OAAO,IAAA,2CAAoB,EAAC,aAAa,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AACxD,CAAC;AAoBD,SAAgB,4BAA4B,CAC1C,eAAuC,EACvC,aAA6C,EAC7C,sBAA+B,EAC/B,0BAAuC;IAEvC,iEAAiE;IACjE,yBAAyB,CACvB,eAAe,CAAC,OAAO,EACvB,aAAa,CAAC,mBAAmB,CAClC,CAAC;IAEF,iFAAiF;IACjF,yEAAyE;IACzE,MAAM,gBAAgB,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;IAC7E,GAAG,CACD,0DAA0D,EAC1D,aAAa,CAAC,QAAQ,EACtB,gBAAgB,CACjB,CAAC;IAEF,MAAM,gBAAgB,GAAG,mBAAI,CAAC,QAAQ,CACpC,aAAa,CAAC,eAAe,EAC7B,gBAAgB,CACjB,CAAC;IACF,MAAM,uBAAuB,GAAG,iBAAiB,CAC/C,gBAAgB,EAChB,eAAe,CAAC,mBAAmB,CACpC,CAAC;IAEF,gDAAgD;IAChD,mEAAmE;IACnE,IAAI,CAAC,sBAAsB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACxD,OAAO,iCAAiC,CACtC,gBAAgB,EAChB,aAAa,EACb,eAAe,CAAC,OAAO,CACxB,CAAC;IACJ,CAAC;IAED,4EAA4E;IAC5E,0CAA0C;IAC1C,iDAAiD;IACjD,6DAA6D;IAC7D,4EAA4E;IAC5E,MAAM,MAAM,GACV,sBAAsB;QACtB,gCAAgC,CAC9B,0BAA0B,EAC1B,uBAAuB,EACvB,gBAAgB,EAChB,aAAa,EACb,eAAe,CAChB,CAAC;IAEJ,GAAG,CAAC,iCAAiC,EAAE,MAAM,CAAC,CAAC;IAE/C,OAAO,wBAAwB,CAC7B,gBAAgB,EAChB,aAAa,EACb,eAAe,CAChB,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CACjB,QAAgB,EAChB,eAAuC;IAEvC,OAAO,mBAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC9B,CAAC,CAAC,QAAQ;QACV,CAAC,CAAC,mBAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,QAAQ,CAAC,CAAC;AAC9E,CAAC;AAED,SAAS,iBAAiB,CACxB,QAAgB,EAChB,mBAAyC;IAEzC,OAAO,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAA,qBAAS,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;AAC9E,CAAC"}