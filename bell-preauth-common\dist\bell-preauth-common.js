/*! bell-preauth-common (lib) 1.0.0 | bwtk 6.1.0 | 2025-07-14T19:07:09.316Z */
var root,factory;root=self,factory=()=>(()=>{"use strict";return{}})(),"object"==typeof exports&&"object"==typeof module?module.exports=factory():"function"==typeof define&&define.amd?define("bell-preauth-common",[],factory):"object"==typeof exports?exports["bell-preauth-common"]=factory():root["bell-preauth-common"]=factory();