import * as React from "react";
import * as ReactDOM from "react-dom";
import { WidgetLoader, Init } from "bwtk";


export function initialize(config: any, root: string, debug: any) {
    config = Object.assign({}, config, {
        "loader.staticWidgetMappings": {
            "bell-preauth-manage": {
                "factory": () => {
                    return require("bell-preauth-manage");
                },
                "namespace": "Preauth/Manage"
            }
        }
    });
    Init(config);

    ReactDOM.render(
        <WidgetLoader widget="bell-preauth-manage" />,
        document.getElementById(root));
}
