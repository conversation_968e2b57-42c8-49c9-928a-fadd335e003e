import * as React from "react";
import * as ReactDOM from "react-dom";
import { Provider } from "react-redux";
import { Widget, ViewWidget, Logger, LoggerConfigKeys } from "bwtk";
import Store  from "./store";
import {App} from "./App";
import Config from "./Config";
 import { createMultiPaymentAction, getPassKey } from "./store/Actions";
 import { AccountInputValues, PaymentItemAccountType } from "./models";
import { getBanSpecificTransactionId } from "./utils";
@Widget({ namespace: "Preauth/Manage" })
export default class extends ViewWidget {
    constructor
    (private store: Store, 
    private config: Config, 
    logger: Logger) {
        super();
        false && logger;
    }

    init() {
        this.config.setConfig(LoggerConfigKeys.SeverityLevel, this.config.logLevel);
        this.store.dispatch(getPassKey({ban: this.config.getPaymentItem[0].Ban, sub: this.config.getPaymentItem[0].subscriberId}));
        if (this.config.getPaymentItem != null && this.config.getPaymentItem.length === 1)
        {
            const paymentItem = this.config.getPaymentItem[0];
            const accountInputValue: AccountInputValues[] = [{
                accountNumber: paymentItem.Ban,
                subNumber: paymentItem.subscriberId, // This can be null or undefined if not provided
                transactionID: getBanSpecificTransactionId(paymentItem.Ban, this.config.transactionIdArray),
                payBalanceAmnt: 0,
            }];
            this.store.dispatch(createMultiPaymentAction({ ban: paymentItem.Ban, type: (paymentItem.AccountType === PaymentItemAccountType.OneBill), details: accountInputValue ,sub: paymentItem.subscriberId }))
        }
    }


    destroy() {
        this.store.destroy();
    }

    render(root: Element) {
        const { store, config } = this;
        ReactDOM.render(
            <Provider {...{ store }}>
                <App Config={config} />
            </Provider>
            , root
        );
    }
}

