{"version": 3, "file": "getWatchProgramsForProjects.js", "sourceRoot": "", "sources": ["../../src/create-program/getWatchProgramsForProjects.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4eS,4CAAgB;AAAE,kEAA2B;AA5etD,kDAA0B;AAC1B,sDAAyB;AACzB,+CAAiC;AAMjC,kDAA8C;AAC9C,qCAKkB;AAElB,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,wDAAwD,CAAC,CAAC;AAE5E;;GAEG;AACH,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAGjC,CAAC;AAEJ;;;GAGG;AACH,MAAM,4BAA4B,GAAG,IAAI,GAAG,EAGzC,CAAC;AACJ,MAAM,8BAA8B,GAAG,IAAI,GAAG,EAG3C,CAAC;AAEJ;;GAEG;AACH,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAqC,CAAC;AAE1E;;GAEG;AACH,MAAM,kCAAkC,GAAG,IAAI,GAAG,EAAyB,CAAC;AAE5E,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAyB,CAAC;AAE7D;;;GAGG;AACH,SAAS,gBAAgB;IACvB,oBAAoB,CAAC,KAAK,EAAE,CAAC;IAC7B,4BAA4B,CAAC,KAAK,EAAE,CAAC;IACrC,8BAA8B,CAAC,KAAK,EAAE,CAAC;IACvC,mBAAmB,CAAC,KAAK,EAAE,CAAC;IAC5B,oBAAoB,CAAC,KAAK,EAAE,CAAC;IAC7B,kCAAkC,CAAC,KAAK,EAAE,CAAC;AAC7C,CAAC;AAED,SAAS,iBAAiB,CACxB,WAAqD;IAErD,OAAO,CACL,QAAgB,EAChB,QAAgC,EAChB,EAAE;QAClB,MAAM,kBAAkB,GAAG,IAAA,6BAAoB,EAAC,QAAQ,CAAC,CAAC;QAC1D,MAAM,QAAQ,GAAG,CAAC,GAAgC,EAAE;YAClD,IAAI,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;gBACrB,WAAW,CAAC,GAAG,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YAChD,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,EAAE,CAAC;QACL,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEvB,OAAO;YACL,KAAK,EAAE,GAAS,EAAE;gBAChB,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC5B,CAAC;SACF,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,yBAAyB,GAG3B;IACF,IAAI,EAAE,EAAE;IACR,QAAQ,EAAE,EAAmB;CAC9B,CAAC;AAEF;;;GAGG;AACH,SAAS,kBAAkB,CAAC,UAAyB;IACnD,MAAM,IAAI,KAAK,CACb,EAAE,CAAC,4BAA4B,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CACxE,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAC3B,YAA2B,EAC3B,OAAmB;IAEnB,MAAM,QAAQ,GAAG,IAAI,GAAG,CACtB,OAAO,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAA,6BAAoB,EAAC,CAAC,CAAC,CAAC,CAC7D,CAAC;IACF,oBAAoB,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACjD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;GAIG;AACH,SAAS,2BAA2B,CAClC,aAA4B;IAE5B,MAAM,QAAQ,GAAG,IAAA,6BAAoB,EAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC9D,MAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,mDAAmD;IACnD,yBAAyB,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;IACpD,yBAAyB,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAE9C,mCAAmC;IACnC,MAAM,kBAAkB,GAAG,4BAA4B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtE,MAAM,QAAQ,GAAG,IAAA,mBAAU,EAAC,IAAA,0BAAW,EAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7D,IACE,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,QAAQ;QAC9C,kBAAkB;QAClB,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAC3B,CAAC;QACD,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAC9B,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,MAAM,2BAA2B,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAEpE;;;OAGG;IACH,KAAK,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,IAAI,oBAAoB,CAAC,OAAO,EAAE,EAAE,CAAC;QAC3E,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;YACnD,gFAAgF;YAChF,wDAAwD;YACxD,EAAE;YACF,gEAAgE;YAChE,uEAAuE;YACvE,SAAS;QACX,CAAC;QACD,IAAI,QAAQ,GAAG,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACtD,IAAI,cAAc,GAAsB,IAAI,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,cAAc,GAAG,aAAa,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,CAAC;YACzD,QAAQ,GAAG,oBAAoB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3B,GAAG,CAAC,qCAAqC,EAAE,QAAQ,CAAC,CAAC;YAErD,cAAc,KAAK,aAAa,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,CAAC;YAC3D,uCAAuC;YACvC,cAAc,CAAC,cAAc,EAAE,CAAC;YAEhC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IACD,GAAG,CACD,2EAA2E,EAC3E,QAAQ,CACT,CAAC;IAEF;;;;OAIG;IACH,KAAK,MAAM,YAAY,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;QAClD,MAAM,aAAa,GAAG,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhE,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,cAAc,GAAG,sBAAsB,CAC3C,aAAa,EACb,QAAQ,EACR,YAAY,CAAC,CAAC,CAAC,CAChB,CAAC;YACF,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,SAAS;YACX,CAAC;YAED,uCAAuC;YACvC,cAAc,CAAC,cAAc,EAAE,CAAC;YAEhC,gCAAgC;YAChC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;YACvE,IAAI,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3B,GAAG,CAAC,oCAAoC,EAAE,QAAQ,CAAC,CAAC;gBACpD,qEAAqE;gBACrE,OAAO,CAAC,cAAc,CAAC,CAAC;YAC1B,CAAC;YAED,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC7B,SAAS;QACX,CAAC;QAED,MAAM,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;QACxE,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;QAExD,MAAM,OAAO,GAAG,YAAY,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,CAAC;QACvD,uCAAuC;QACvC,OAAO,CAAC,cAAc,EAAE,CAAC;QAEzB,gCAAgC;QAChC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAChE,IAAI,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3B,GAAG,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;YAC5C,qEAAqE;YACrE,OAAO,CAAC,OAAO,CAAC,CAAC;QACnB,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACxB,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,kBAAkB,CACzB,YAAoB,EACpB,aAA4B;IAE5B,GAAG,CAAC,gCAAgC,EAAE,YAAY,CAAC,CAAC;IAEpD,uBAAuB;IACvB,MAAM,iBAAiB,GAAG,EAAE,CAAC,uBAAuB,CAClD,YAAY,EACZ,IAAA,8CAAqC,EAAC,aAAa,CAAC,EACpD,EAAE,CAAC,GAAG,EACN,EAAE,CAAC,qBAAqB,EACxB,kBAAkB;IAClB,6DAA6D;IAC7D,gEAAgE;IAChE,qBAAqB,CAAC,GAAG,EAAE,GAAE,CAAC,CACqB,CAAC;IACtD,iBAAiB,CAAC,gBAAgB,GAAG,aAAa,CAAC,gBAAgB,CAAC;IAEpE,0EAA0E;IAC1E,MAAM,WAAW,GAAG,iBAAiB,CAAC,QAAQ,CAAC;IAC/C,iBAAiB,CAAC,QAAQ,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAsB,EAAE;QACxE,MAAM,QAAQ,GAAG,IAAA,6BAAoB,EAAC,UAAU,CAAC,CAAC;QAClD,MAAM,WAAW,GACf,QAAQ,KAAK,yBAAyB,CAAC,QAAQ;YAC7C,CAAC,CAAC,IAAA,0BAAW,EAAC,yBAAyB,CAAC,IAAI,CAAC;YAC7C,CAAC,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACtC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC9B,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAA,mBAAU,EAAC,WAAW,CAAC,CAAC,CAAC;QAC7D,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC,CAAC;IAEF,iFAAiF;IACjF,iBAAiB,CAAC,mCAAmC,GAAG,kBAAkB,CAAC;IAE3E,uCAAuC;IACvC,iBAAiB,CAAC,kBAAkB,GAAG,CAAC,OAAO,EAAQ,EAAE;QACvD,0DAA0D;QAC1D,MAAM,qBAAqB,GAAG,OAAO;aAClC,+BAA+B,EAAE;aACjC,MAAM,CACL,IAAI,CAAC,EAAE,CACL,IAAI,CAAC,QAAQ,KAAK,EAAE,CAAC,kBAAkB,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,CACvE,CAAC;QACJ,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC,CAAC;IAEF;;;;;;;;;OASG;IACH,iBAAiB,CAAC,SAAS,GAAG,iBAAiB,CAAC,4BAA4B,CAAC,CAAC;IAC9E,iBAAiB,CAAC,cAAc,GAAG,iBAAiB,CAClD,8BAA8B,CAC/B,CAAC;IAEF,sFAAsF;IACtF,MAAM,iCAAiC,GACrC,iBAAiB,CAAC,oCAAoC,CAAC;IACzD,iBAAiB,CAAC,oCAAoC,GAAG,CAAC,IAAI,EAAQ,EAAE;QACtE,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,CACnB,IAAI,EACJ,UAAU,EACV,OAAO,EACP,OAAO,EACP,KAAK,EACK,EAAE,CACZ,gBAAgB,CACd,IAAI,EACJ,CAAC,UAAU;YACT,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,CAAC,GAAG,UAAU,EAAE,GAAG,aAAa,CAAC,mBAAmB,CAAC,EACzD,OAAO,EACP,OAAO,EACP,KAAK,CACN,CAAC;QACJ,iCAAiC,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC,CAAC;IACF,yBAAyB;IACzB,iBAAiB,CAAC,mBAAmB,GAAG,aAAa,CAAC,mBAAmB,CAAC,GAAG,CAC3E,SAAS,CAAC,EAAE,CAAC,CAAC;QACZ,SAAS;QACT,cAAc,EAAE,IAAI;QACpB,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ;KACnC,CAAC,CACH,CAAC;IACF,iBAAiB,CAAC,KAAK,GAAG,GAAG,CAAC;IAE9B,0FAA0F;IAC1F,+FAA+F;IAC/F,iBAAiB,CAAC,UAAU,GAAG,SAAS,CAAC;IACzC,iBAAiB,CAAC,YAAY,GAAG,SAAS,CAAC;IAC3C,OAAO,EAAE,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,kBAAkB,CAAC,YAA2B;IACrD,MAAM,IAAI,GAAG,iBAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACvC,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC;IACpC,MAAM,oBAAoB,GACxB,kCAAkC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAEvD,kCAAkC,CAAC,GAAG,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;IAErE,IAAI,oBAAoB,KAAK,SAAS,EAAE,CAAC;QACvC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC,GAAG,CAAC,oBAAoB,GAAG,cAAc,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC;AAC1E,CAAC;AAED,SAAS,sBAAsB,CAC7B,aAAsD,EACtD,QAAuB,EACvB,YAA2B;IAE3B;;;OAGG;IACH,IAAI,cAAc,GAAG,aAAa,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,CAAC;IAE7D,qEAAqE;IACrE,+EAA+E;IAC/E,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,MAAM,EAAE,CAAC;QACpD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,IAAI,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC;QACrC;;;WAGG;QACH,GAAG,CAAC,sDAAsD,EAAE,YAAY,CAAC,CAAC;QAC1E,oEAAoE;QACpE,4BAA4B;aACzB,GAAG,CAAC,YAAY,CAAE;aAClB,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,EAAE,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC;QAEpE,wFAAwF;QACxF,oBAAoB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACxD,IAAI,UAAU,EAAE,CAAC;QACf,OAAO,cAAc,CAAC;IACxB,CAAC;IACD;;;OAGG;IACH,GAAG,CAAC,8DAA8D,EAAE,QAAQ,CAAC,CAAC;IAE9E,kEAAkE;IAClE,MAAM,UAAU,GAAG,IAAA,yBAAgB,EAAC,QAAQ,CAAC,CAAC;IAC9C,IAAI,OAAO,GAAyB,IAAI,CAAC;IACzC,IAAI,IAAI,GAAG,UAAU,CAAC;IACtB,IAAI,WAAW,GAAG,KAAK,CAAC;IACxB,OAAO,OAAO,KAAK,IAAI,EAAE,CAAC;QACxB,OAAO,GAAG,IAAI,CAAC;QACf,MAAM,oBAAoB,GAAG,8BAA8B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,oBAAoB,EAAE,CAAC;YACzB,KAAK,MAAM,EAAE,IAAI,oBAAoB,EAAE,CAAC;gBACtC,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;oBAC3B,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBAClD,CAAC;gBACD,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAC/C,CAAC;YACD,WAAW,GAAG,IAAI,CAAC;QACrB,CAAC;QAED,IAAI,GAAG,IAAA,yBAAgB,EAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IACD,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB;;;WAGG;QACH,GAAG,CAAC,0DAA0D,EAAE,QAAQ,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC;IACd,CAAC;IAED,yFAAyF;IACzF,oBAAoB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAE1C,6BAA6B;IAC7B,cAAc,GAAG,aAAa,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,CAAC;IACzD,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACpD,IAAI,UAAU,EAAE,CAAC;QACf,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;;;;;OAMG;IACH,GAAG,CACD,0FAA0F,EAC1F,QAAQ,CACT,CAAC;IAEF,MAAM,aAAa,GAAG,cAAc,CAAC,gBAAgB,EAAE,CAAC;IACxD,6FAA6F;IAC7F,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,iBAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IACrE,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,sGAAsG;QACtG,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,kBAAkB,GAAG,4BAA4B,CAAC,GAAG,CACzD,IAAA,6BAAoB,EAAC,WAAW,CAAC,CAClC,CAAC;IACF,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxB,qCAAqC;QACrC,GAAG,CAAC,kDAAkD,EAAE,WAAW,CAAC,CAAC;QACrE,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,GAAG,CAAC,6BAA6B,EAAE,WAAW,CAAC,CAAC;IAChD,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAC9B,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,oBAAoB,CAAC,OAAO,CAAC,CACjD,CAAC;IAEF,2EAA2E;IAC3E,oBAAoB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAE1C,cAAc,GAAG,aAAa,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,CAAC;IACzD,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACpD,IAAI,UAAU,EAAE,CAAC;QACf,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,GAAG,CACD,uGAAuG,EACvG,QAAQ,CACT,CAAC;IACF,OAAO,IAAI,CAAC;AACd,CAAC"}