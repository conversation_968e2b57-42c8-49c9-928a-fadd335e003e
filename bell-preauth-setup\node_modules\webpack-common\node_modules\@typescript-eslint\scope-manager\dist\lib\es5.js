"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es5 = void 0;
const base_config_1 = require("./base-config");
const decorators_1 = require("./decorators");
const decorators_legacy_1 = require("./decorators.legacy");
exports.es5 = {
    ...decorators_1.decorators,
    ...decorators_legacy_1.decorators_legacy,
    Array: base_config_1.TYPE_VALUE,
    ArrayBuffer: base_config_1.TYPE_VALUE,
    ArrayBufferConstructor: base_config_1.TYPE,
    ArrayBufferLike: base_config_1.TYPE,
    ArrayBufferTypes: base_config_1.TYPE,
    ArrayBufferView: base_config_1.TYPE,
    ArrayConstructor: base_config_1.TYPE,
    ArrayLike: base_config_1.TYPE,
    Awaited: base_config_1.TYPE,
    Boolean: base_config_1.TYPE_VALUE,
    BooleanConstructor: base_config_1.TYPE,
    CallableFunction: base_config_1.TYPE,
    Capitalize: base_config_1.TYPE,
    ConcatArray: base_config_1.TYPE,
    ConstructorParameters: base_config_1.TYPE,
    DataView: base_config_1.TYPE_VALUE,
    DataViewConstructor: base_config_1.TYPE,
    Date: base_config_1.TYPE_VALUE,
    DateConstructor: base_config_1.TYPE,
    Error: base_config_1.TYPE_VALUE,
    ErrorConstructor: base_config_1.TYPE,
    EvalError: base_config_1.TYPE_VALUE,
    EvalErrorConstructor: base_config_1.TYPE,
    Exclude: base_config_1.TYPE,
    Extract: base_config_1.TYPE,
    Float32Array: base_config_1.TYPE_VALUE,
    Float32ArrayConstructor: base_config_1.TYPE,
    Float64Array: base_config_1.TYPE_VALUE,
    Float64ArrayConstructor: base_config_1.TYPE,
    Function: base_config_1.TYPE_VALUE,
    FunctionConstructor: base_config_1.TYPE,
    IArguments: base_config_1.TYPE,
    ImportAssertions: base_config_1.TYPE,
    ImportAttributes: base_config_1.TYPE,
    ImportCallOptions: base_config_1.TYPE,
    ImportMeta: base_config_1.TYPE,
    InstanceType: base_config_1.TYPE,
    Int8Array: base_config_1.TYPE_VALUE,
    Int8ArrayConstructor: base_config_1.TYPE,
    Int16Array: base_config_1.TYPE_VALUE,
    Int16ArrayConstructor: base_config_1.TYPE,
    Int32Array: base_config_1.TYPE_VALUE,
    Int32ArrayConstructor: base_config_1.TYPE,
    Intl: base_config_1.TYPE_VALUE,
    JSON: base_config_1.TYPE_VALUE,
    Lowercase: base_config_1.TYPE,
    Math: base_config_1.TYPE_VALUE,
    NewableFunction: base_config_1.TYPE,
    NoInfer: base_config_1.TYPE,
    NonNullable: base_config_1.TYPE,
    Number: base_config_1.TYPE_VALUE,
    NumberConstructor: base_config_1.TYPE,
    Object: base_config_1.TYPE_VALUE,
    ObjectConstructor: base_config_1.TYPE,
    Omit: base_config_1.TYPE,
    OmitThisParameter: base_config_1.TYPE,
    Parameters: base_config_1.TYPE,
    Partial: base_config_1.TYPE,
    Pick: base_config_1.TYPE,
    Promise: base_config_1.TYPE,
    PromiseConstructorLike: base_config_1.TYPE,
    PromiseLike: base_config_1.TYPE,
    PropertyDescriptor: base_config_1.TYPE,
    PropertyDescriptorMap: base_config_1.TYPE,
    PropertyKey: base_config_1.TYPE,
    RangeError: base_config_1.TYPE_VALUE,
    RangeErrorConstructor: base_config_1.TYPE,
    Readonly: base_config_1.TYPE,
    ReadonlyArray: base_config_1.TYPE,
    Record: base_config_1.TYPE,
    ReferenceError: base_config_1.TYPE_VALUE,
    ReferenceErrorConstructor: base_config_1.TYPE,
    RegExp: base_config_1.TYPE_VALUE,
    RegExpConstructor: base_config_1.TYPE,
    RegExpExecArray: base_config_1.TYPE,
    RegExpMatchArray: base_config_1.TYPE,
    Required: base_config_1.TYPE,
    ReturnType: base_config_1.TYPE,
    String: base_config_1.TYPE_VALUE,
    StringConstructor: base_config_1.TYPE,
    Symbol: base_config_1.TYPE,
    SyntaxError: base_config_1.TYPE_VALUE,
    SyntaxErrorConstructor: base_config_1.TYPE,
    TemplateStringsArray: base_config_1.TYPE,
    ThisParameterType: base_config_1.TYPE,
    ThisType: base_config_1.TYPE,
    TypedPropertyDescriptor: base_config_1.TYPE,
    TypeError: base_config_1.TYPE_VALUE,
    TypeErrorConstructor: base_config_1.TYPE,
    Uint8Array: base_config_1.TYPE_VALUE,
    Uint8ArrayConstructor: base_config_1.TYPE,
    Uint8ClampedArray: base_config_1.TYPE_VALUE,
    Uint8ClampedArrayConstructor: base_config_1.TYPE,
    Uint16Array: base_config_1.TYPE_VALUE,
    Uint16ArrayConstructor: base_config_1.TYPE,
    Uint32Array: base_config_1.TYPE_VALUE,
    Uint32ArrayConstructor: base_config_1.TYPE,
    Uncapitalize: base_config_1.TYPE,
    Uppercase: base_config_1.TYPE,
    URIError: base_config_1.TYPE_VALUE,
    URIErrorConstructor: base_config_1.TYPE,
    WeakKey: base_config_1.TYPE,
    WeakKeyTypes: base_config_1.TYPE,
};
//# sourceMappingURL=es5.js.map