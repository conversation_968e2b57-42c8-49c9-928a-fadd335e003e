[{"C:\\Users\\<USER>\\WebApplications\\AP-SMB\\bell-preauth-setup-bundle\\src\\index.tsx": "1"}, {"size": 629, "mtime": 1752499613400, "results": "2", "hashOfConfig": "3"}, {"filePath": "4", "messages": "5", "suppressedMessages": "6", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "lto4bz", "C:\\Users\\<USER>\\WebApplications\\AP-SMB\\bell-preauth-setup-bundle\\src\\index.tsx", ["7", "8"], [], {"ruleId": "9", "severity": 1, "message": "10", "line": 6, "column": 36, "nodeType": "11", "messageId": "12", "endLine": 6, "endColumn": 39, "suggestions": "13"}, {"ruleId": "9", "severity": 1, "message": "10", "line": 6, "column": 62, "nodeType": "11", "messageId": "12", "endLine": 6, "endColumn": 65, "suggestions": "14"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["15", "16"], ["17", "18"], {"messageId": "19", "fix": "20", "desc": "21"}, {"messageId": "22", "fix": "23", "desc": "24"}, {"messageId": "19", "fix": "25", "desc": "21"}, {"messageId": "22", "fix": "26", "desc": "24"}, "suggestUnknown", {"range": "27", "text": "28"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "29", "text": "30"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "31", "text": "28"}, {"range": "32", "text": "30"}, [164, 167], "unknown", [164, 167], "never", [190, 193], [190, 193]]