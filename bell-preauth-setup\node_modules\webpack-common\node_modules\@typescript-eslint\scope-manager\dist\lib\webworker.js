"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.webworker = void 0;
const base_config_1 = require("./base-config");
exports.webworker = {
    AbortController: base_config_1.TYPE_VALUE,
    AbortSignal: base_config_1.TYPE_VALUE,
    AbortSignalEventMap: base_config_1.TYPE,
    AbstractWorker: base_config_1.TYPE,
    AbstractWorkerEventMap: base_config_1.TYPE,
    AddEventListenerOptions: base_config_1.TYPE,
    AesCbcParams: base_config_1.TYPE,
    AesCtrParams: base_config_1.TYPE,
    AesDerivedKeyParams: base_config_1.TYPE,
    AesGcmParams: base_config_1.TYPE,
    AesKeyAlgorithm: base_config_1.TYPE,
    AesKeyGenParams: base_config_1.TYPE,
    Algorithm: base_config_1.TYPE,
    AlgorithmIdentifier: base_config_1.TYPE,
    AllowSharedBufferSource: base_config_1.TYPE,
    AlphaOption: base_config_1.TYPE,
    ANGLE_instanced_arrays: base_config_1.TYPE,
    AnimationFrameProvider: base_config_1.TYPE,
    AudioConfiguration: base_config_1.TYPE,
    AvcBitstreamFormat: base_config_1.TYPE,
    AvcEncoderConfig: base_config_1.TYPE,
    BigInteger: base_config_1.TYPE,
    BinaryData: base_config_1.TYPE,
    BinaryType: base_config_1.TYPE,
    Blob: base_config_1.TYPE_VALUE,
    BlobPart: base_config_1.TYPE,
    BlobPropertyBag: base_config_1.TYPE,
    Body: base_config_1.TYPE,
    BodyInit: base_config_1.TYPE,
    BroadcastChannel: base_config_1.TYPE_VALUE,
    BroadcastChannelEventMap: base_config_1.TYPE,
    BufferSource: base_config_1.TYPE,
    ByteLengthQueuingStrategy: base_config_1.TYPE_VALUE,
    Cache: base_config_1.TYPE_VALUE,
    CacheQueryOptions: base_config_1.TYPE,
    CacheStorage: base_config_1.TYPE_VALUE,
    CanvasCompositing: base_config_1.TYPE,
    CanvasDirection: base_config_1.TYPE,
    CanvasDrawImage: base_config_1.TYPE,
    CanvasDrawPath: base_config_1.TYPE,
    CanvasFillRule: base_config_1.TYPE,
    CanvasFillStrokeStyles: base_config_1.TYPE,
    CanvasFilters: base_config_1.TYPE,
    CanvasFontKerning: base_config_1.TYPE,
    CanvasFontStretch: base_config_1.TYPE,
    CanvasFontVariantCaps: base_config_1.TYPE,
    CanvasGradient: base_config_1.TYPE_VALUE,
    CanvasImageData: base_config_1.TYPE,
    CanvasImageSmoothing: base_config_1.TYPE,
    CanvasImageSource: base_config_1.TYPE,
    CanvasLineCap: base_config_1.TYPE,
    CanvasLineJoin: base_config_1.TYPE,
    CanvasPath: base_config_1.TYPE,
    CanvasPathDrawingStyles: base_config_1.TYPE,
    CanvasPattern: base_config_1.TYPE_VALUE,
    CanvasRect: base_config_1.TYPE,
    CanvasShadowStyles: base_config_1.TYPE,
    CanvasState: base_config_1.TYPE,
    CanvasText: base_config_1.TYPE,
    CanvasTextAlign: base_config_1.TYPE,
    CanvasTextBaseline: base_config_1.TYPE,
    CanvasTextDrawingStyles: base_config_1.TYPE,
    CanvasTextRendering: base_config_1.TYPE,
    CanvasTransform: base_config_1.TYPE,
    Client: base_config_1.TYPE_VALUE,
    ClientQueryOptions: base_config_1.TYPE,
    Clients: base_config_1.TYPE_VALUE,
    ClientTypes: base_config_1.TYPE,
    CloseEvent: base_config_1.TYPE_VALUE,
    CloseEventInit: base_config_1.TYPE,
    CodecState: base_config_1.TYPE,
    ColorGamut: base_config_1.TYPE,
    ColorSpaceConversion: base_config_1.TYPE,
    CompressionFormat: base_config_1.TYPE,
    CompressionStream: base_config_1.TYPE_VALUE,
    Console: base_config_1.TYPE,
    CountQueuingStrategy: base_config_1.TYPE_VALUE,
    Crypto: base_config_1.TYPE_VALUE,
    CryptoKey: base_config_1.TYPE_VALUE,
    CryptoKeyPair: base_config_1.TYPE,
    CSSImageValue: base_config_1.TYPE_VALUE,
    CSSKeywordish: base_config_1.TYPE,
    CSSKeywordValue: base_config_1.TYPE_VALUE,
    CSSMathClamp: base_config_1.TYPE_VALUE,
    CSSMathInvert: base_config_1.TYPE_VALUE,
    CSSMathMax: base_config_1.TYPE_VALUE,
    CSSMathMin: base_config_1.TYPE_VALUE,
    CSSMathNegate: base_config_1.TYPE_VALUE,
    CSSMathOperator: base_config_1.TYPE,
    CSSMathProduct: base_config_1.TYPE_VALUE,
    CSSMathSum: base_config_1.TYPE_VALUE,
    CSSMathValue: base_config_1.TYPE_VALUE,
    CSSMatrixComponent: base_config_1.TYPE_VALUE,
    CSSMatrixComponentOptions: base_config_1.TYPE,
    CSSNumberish: base_config_1.TYPE,
    CSSNumericArray: base_config_1.TYPE_VALUE,
    CSSNumericBaseType: base_config_1.TYPE,
    CSSNumericType: base_config_1.TYPE,
    CSSNumericValue: base_config_1.TYPE_VALUE,
    CSSPerspective: base_config_1.TYPE_VALUE,
    CSSPerspectiveValue: base_config_1.TYPE,
    CSSRotate: base_config_1.TYPE_VALUE,
    CSSScale: base_config_1.TYPE_VALUE,
    CSSSkew: base_config_1.TYPE_VALUE,
    CSSSkewX: base_config_1.TYPE_VALUE,
    CSSSkewY: base_config_1.TYPE_VALUE,
    CSSStyleValue: base_config_1.TYPE_VALUE,
    CSSTransformComponent: base_config_1.TYPE_VALUE,
    CSSTransformValue: base_config_1.TYPE_VALUE,
    CSSTranslate: base_config_1.TYPE_VALUE,
    CSSUnitValue: base_config_1.TYPE_VALUE,
    CSSUnparsedSegment: base_config_1.TYPE,
    CSSUnparsedValue: base_config_1.TYPE_VALUE,
    CSSVariableReferenceValue: base_config_1.TYPE_VALUE,
    CustomEvent: base_config_1.TYPE_VALUE,
    CustomEventInit: base_config_1.TYPE,
    DecompressionStream: base_config_1.TYPE_VALUE,
    DedicatedWorkerGlobalScope: base_config_1.TYPE_VALUE,
    DedicatedWorkerGlobalScopeEventMap: base_config_1.TYPE,
    DocumentVisibilityState: base_config_1.TYPE,
    DOMException: base_config_1.TYPE_VALUE,
    DOMHighResTimeStamp: base_config_1.TYPE,
    DOMMatrix: base_config_1.TYPE_VALUE,
    DOMMatrix2DInit: base_config_1.TYPE,
    DOMMatrixInit: base_config_1.TYPE,
    DOMMatrixReadOnly: base_config_1.TYPE_VALUE,
    DOMPoint: base_config_1.TYPE_VALUE,
    DOMPointInit: base_config_1.TYPE,
    DOMPointReadOnly: base_config_1.TYPE_VALUE,
    DOMQuad: base_config_1.TYPE_VALUE,
    DOMQuadInit: base_config_1.TYPE,
    DOMRect: base_config_1.TYPE_VALUE,
    DOMRectInit: base_config_1.TYPE,
    DOMRectReadOnly: base_config_1.TYPE_VALUE,
    DOMStringList: base_config_1.TYPE_VALUE,
    EcdhKeyDeriveParams: base_config_1.TYPE,
    EcdsaParams: base_config_1.TYPE,
    EcKeyGenParams: base_config_1.TYPE,
    EcKeyImportParams: base_config_1.TYPE,
    EncodedVideoChunk: base_config_1.TYPE_VALUE,
    EncodedVideoChunkInit: base_config_1.TYPE,
    EncodedVideoChunkMetadata: base_config_1.TYPE,
    EncodedVideoChunkOutputCallback: base_config_1.TYPE,
    EncodedVideoChunkType: base_config_1.TYPE,
    EndingType: base_config_1.TYPE,
    EpochTimeStamp: base_config_1.TYPE,
    ErrorEvent: base_config_1.TYPE_VALUE,
    ErrorEventInit: base_config_1.TYPE,
    Event: base_config_1.TYPE_VALUE,
    EventInit: base_config_1.TYPE,
    EventListener: base_config_1.TYPE,
    EventListenerObject: base_config_1.TYPE,
    EventListenerOptions: base_config_1.TYPE,
    EventListenerOrEventListenerObject: base_config_1.TYPE,
    EventSource: base_config_1.TYPE_VALUE,
    EventSourceEventMap: base_config_1.TYPE,
    EventSourceInit: base_config_1.TYPE,
    EventTarget: base_config_1.TYPE_VALUE,
    EXT_blend_minmax: base_config_1.TYPE,
    EXT_color_buffer_float: base_config_1.TYPE,
    EXT_color_buffer_half_float: base_config_1.TYPE,
    EXT_float_blend: base_config_1.TYPE,
    EXT_frag_depth: base_config_1.TYPE,
    EXT_shader_texture_lod: base_config_1.TYPE,
    EXT_sRGB: base_config_1.TYPE,
    EXT_texture_compression_bptc: base_config_1.TYPE,
    EXT_texture_compression_rgtc: base_config_1.TYPE,
    EXT_texture_filter_anisotropic: base_config_1.TYPE,
    EXT_texture_norm16: base_config_1.TYPE,
    ExtendableEvent: base_config_1.TYPE_VALUE,
    ExtendableEventInit: base_config_1.TYPE,
    ExtendableMessageEvent: base_config_1.TYPE_VALUE,
    ExtendableMessageEventInit: base_config_1.TYPE,
    FetchEvent: base_config_1.TYPE_VALUE,
    FetchEventInit: base_config_1.TYPE,
    File: base_config_1.TYPE_VALUE,
    FileList: base_config_1.TYPE_VALUE,
    FilePropertyBag: base_config_1.TYPE,
    FileReader: base_config_1.TYPE_VALUE,
    FileReaderEventMap: base_config_1.TYPE,
    FileReaderSync: base_config_1.TYPE_VALUE,
    FileSystemCreateWritableOptions: base_config_1.TYPE,
    FileSystemDirectoryHandle: base_config_1.TYPE_VALUE,
    FileSystemFileHandle: base_config_1.TYPE_VALUE,
    FileSystemGetDirectoryOptions: base_config_1.TYPE,
    FileSystemGetFileOptions: base_config_1.TYPE,
    FileSystemHandle: base_config_1.TYPE_VALUE,
    FileSystemHandleKind: base_config_1.TYPE,
    FileSystemReadWriteOptions: base_config_1.TYPE,
    FileSystemRemoveOptions: base_config_1.TYPE,
    FileSystemSyncAccessHandle: base_config_1.TYPE_VALUE,
    FileSystemWritableFileStream: base_config_1.TYPE_VALUE,
    FileSystemWriteChunkType: base_config_1.TYPE,
    Float32List: base_config_1.TYPE,
    FontDisplay: base_config_1.TYPE,
    FontFace: base_config_1.TYPE_VALUE,
    FontFaceDescriptors: base_config_1.TYPE,
    FontFaceLoadStatus: base_config_1.TYPE,
    FontFaceSet: base_config_1.TYPE_VALUE,
    FontFaceSetEventMap: base_config_1.TYPE,
    FontFaceSetLoadEvent: base_config_1.TYPE_VALUE,
    FontFaceSetLoadEventInit: base_config_1.TYPE,
    FontFaceSetLoadStatus: base_config_1.TYPE,
    FontFaceSource: base_config_1.TYPE,
    FormData: base_config_1.TYPE_VALUE,
    FormDataEntryValue: base_config_1.TYPE,
    FrameRequestCallback: base_config_1.TYPE,
    FrameType: base_config_1.TYPE,
    GenericTransformStream: base_config_1.TYPE,
    GetNotificationOptions: base_config_1.TYPE,
    GLbitfield: base_config_1.TYPE,
    GLboolean: base_config_1.TYPE,
    GLclampf: base_config_1.TYPE,
    GLenum: base_config_1.TYPE,
    GLfloat: base_config_1.TYPE,
    GLint: base_config_1.TYPE,
    GLint64: base_config_1.TYPE,
    GLintptr: base_config_1.TYPE,
    GlobalCompositeOperation: base_config_1.TYPE,
    GLsizei: base_config_1.TYPE,
    GLsizeiptr: base_config_1.TYPE,
    GLuint: base_config_1.TYPE,
    GLuint64: base_config_1.TYPE,
    HardwareAcceleration: base_config_1.TYPE,
    HashAlgorithmIdentifier: base_config_1.TYPE,
    HdrMetadataType: base_config_1.TYPE,
    Headers: base_config_1.TYPE_VALUE,
    HeadersInit: base_config_1.TYPE,
    HkdfParams: base_config_1.TYPE,
    HmacImportParams: base_config_1.TYPE,
    HmacKeyGenParams: base_config_1.TYPE,
    IDBCursor: base_config_1.TYPE_VALUE,
    IDBCursorDirection: base_config_1.TYPE,
    IDBCursorWithValue: base_config_1.TYPE_VALUE,
    IDBDatabase: base_config_1.TYPE_VALUE,
    IDBDatabaseEventMap: base_config_1.TYPE,
    IDBDatabaseInfo: base_config_1.TYPE,
    IDBFactory: base_config_1.TYPE_VALUE,
    IDBIndex: base_config_1.TYPE_VALUE,
    IDBIndexParameters: base_config_1.TYPE,
    IDBKeyRange: base_config_1.TYPE_VALUE,
    IDBObjectStore: base_config_1.TYPE_VALUE,
    IDBObjectStoreParameters: base_config_1.TYPE,
    IDBOpenDBRequest: base_config_1.TYPE_VALUE,
    IDBOpenDBRequestEventMap: base_config_1.TYPE,
    IDBRequest: base_config_1.TYPE_VALUE,
    IDBRequestEventMap: base_config_1.TYPE,
    IDBRequestReadyState: base_config_1.TYPE,
    IDBTransaction: base_config_1.TYPE_VALUE,
    IDBTransactionDurability: base_config_1.TYPE,
    IDBTransactionEventMap: base_config_1.TYPE,
    IDBTransactionMode: base_config_1.TYPE,
    IDBTransactionOptions: base_config_1.TYPE,
    IDBValidKey: base_config_1.TYPE,
    IDBVersionChangeEvent: base_config_1.TYPE_VALUE,
    IDBVersionChangeEventInit: base_config_1.TYPE,
    ImageBitmap: base_config_1.TYPE_VALUE,
    ImageBitmapOptions: base_config_1.TYPE,
    ImageBitmapRenderingContext: base_config_1.TYPE_VALUE,
    ImageBitmapRenderingContextSettings: base_config_1.TYPE,
    ImageBitmapSource: base_config_1.TYPE,
    ImageData: base_config_1.TYPE_VALUE,
    ImageDataSettings: base_config_1.TYPE,
    ImageEncodeOptions: base_config_1.TYPE,
    ImageOrientation: base_config_1.TYPE,
    ImageSmoothingQuality: base_config_1.TYPE,
    ImportMeta: base_config_1.TYPE,
    Int32List: base_config_1.TYPE,
    JsonWebKey: base_config_1.TYPE,
    KeyAlgorithm: base_config_1.TYPE,
    KeyFormat: base_config_1.TYPE,
    KeyType: base_config_1.TYPE,
    KeyUsage: base_config_1.TYPE,
    KHR_parallel_shader_compile: base_config_1.TYPE,
    LatencyMode: base_config_1.TYPE,
    Lock: base_config_1.TYPE_VALUE,
    LockGrantedCallback: base_config_1.TYPE,
    LockInfo: base_config_1.TYPE,
    LockManager: base_config_1.TYPE_VALUE,
    LockManagerSnapshot: base_config_1.TYPE,
    LockMode: base_config_1.TYPE,
    LockOptions: base_config_1.TYPE,
    MediaCapabilities: base_config_1.TYPE_VALUE,
    MediaCapabilitiesDecodingInfo: base_config_1.TYPE,
    MediaCapabilitiesEncodingInfo: base_config_1.TYPE,
    MediaCapabilitiesInfo: base_config_1.TYPE,
    MediaConfiguration: base_config_1.TYPE,
    MediaDecodingConfiguration: base_config_1.TYPE,
    MediaDecodingType: base_config_1.TYPE,
    MediaEncodingConfiguration: base_config_1.TYPE,
    MediaEncodingType: base_config_1.TYPE,
    MediaSourceHandle: base_config_1.TYPE_VALUE,
    MediaStreamTrackProcessor: base_config_1.TYPE_VALUE,
    MediaStreamTrackProcessorInit: base_config_1.TYPE,
    MessageChannel: base_config_1.TYPE_VALUE,
    MessageEvent: base_config_1.TYPE_VALUE,
    MessageEventInit: base_config_1.TYPE,
    MessageEventSource: base_config_1.TYPE,
    MessagePort: base_config_1.TYPE_VALUE,
    MessagePortEventMap: base_config_1.TYPE,
    MultiCacheQueryOptions: base_config_1.TYPE,
    NamedCurve: base_config_1.TYPE,
    NavigationPreloadManager: base_config_1.TYPE_VALUE,
    NavigationPreloadState: base_config_1.TYPE,
    NavigatorBadge: base_config_1.TYPE,
    NavigatorConcurrentHardware: base_config_1.TYPE,
    NavigatorID: base_config_1.TYPE,
    NavigatorLanguage: base_config_1.TYPE,
    NavigatorLocks: base_config_1.TYPE,
    NavigatorOnLine: base_config_1.TYPE,
    NavigatorStorage: base_config_1.TYPE,
    Notification: base_config_1.TYPE_VALUE,
    NotificationDirection: base_config_1.TYPE,
    NotificationEvent: base_config_1.TYPE_VALUE,
    NotificationEventInit: base_config_1.TYPE,
    NotificationEventMap: base_config_1.TYPE,
    NotificationOptions: base_config_1.TYPE,
    NotificationPermission: base_config_1.TYPE,
    OES_draw_buffers_indexed: base_config_1.TYPE,
    OES_element_index_uint: base_config_1.TYPE,
    OES_fbo_render_mipmap: base_config_1.TYPE,
    OES_standard_derivatives: base_config_1.TYPE,
    OES_texture_float: base_config_1.TYPE,
    OES_texture_float_linear: base_config_1.TYPE,
    OES_texture_half_float: base_config_1.TYPE,
    OES_texture_half_float_linear: base_config_1.TYPE,
    OES_vertex_array_object: base_config_1.TYPE,
    OffscreenCanvas: base_config_1.TYPE_VALUE,
    OffscreenCanvasEventMap: base_config_1.TYPE,
    OffscreenCanvasRenderingContext2D: base_config_1.TYPE_VALUE,
    OffscreenRenderingContext: base_config_1.TYPE,
    OffscreenRenderingContextId: base_config_1.TYPE,
    OnErrorEventHandler: base_config_1.TYPE,
    OnErrorEventHandlerNonNull: base_config_1.TYPE,
    OVR_multiview2: base_config_1.TYPE,
    Path2D: base_config_1.TYPE_VALUE,
    Pbkdf2Params: base_config_1.TYPE,
    Performance: base_config_1.TYPE_VALUE,
    PerformanceEntry: base_config_1.TYPE_VALUE,
    PerformanceEntryList: base_config_1.TYPE,
    PerformanceEventMap: base_config_1.TYPE,
    PerformanceMark: base_config_1.TYPE_VALUE,
    PerformanceMarkOptions: base_config_1.TYPE,
    PerformanceMeasure: base_config_1.TYPE_VALUE,
    PerformanceMeasureOptions: base_config_1.TYPE,
    PerformanceObserver: base_config_1.TYPE_VALUE,
    PerformanceObserverCallback: base_config_1.TYPE,
    PerformanceObserverEntryList: base_config_1.TYPE_VALUE,
    PerformanceObserverInit: base_config_1.TYPE,
    PerformanceResourceTiming: base_config_1.TYPE_VALUE,
    PerformanceServerTiming: base_config_1.TYPE_VALUE,
    PermissionDescriptor: base_config_1.TYPE,
    PermissionName: base_config_1.TYPE,
    Permissions: base_config_1.TYPE_VALUE,
    PermissionState: base_config_1.TYPE,
    PermissionStatus: base_config_1.TYPE_VALUE,
    PermissionStatusEventMap: base_config_1.TYPE,
    PlaneLayout: base_config_1.TYPE,
    PredefinedColorSpace: base_config_1.TYPE,
    PremultiplyAlpha: base_config_1.TYPE,
    ProgressEvent: base_config_1.TYPE_VALUE,
    ProgressEventInit: base_config_1.TYPE,
    PromiseRejectionEvent: base_config_1.TYPE_VALUE,
    PromiseRejectionEventInit: base_config_1.TYPE,
    PushEncryptionKeyName: base_config_1.TYPE,
    PushEvent: base_config_1.TYPE_VALUE,
    PushEventInit: base_config_1.TYPE,
    PushManager: base_config_1.TYPE_VALUE,
    PushMessageData: base_config_1.TYPE_VALUE,
    PushMessageDataInit: base_config_1.TYPE,
    PushSubscription: base_config_1.TYPE_VALUE,
    PushSubscriptionJSON: base_config_1.TYPE,
    PushSubscriptionOptions: base_config_1.TYPE_VALUE,
    PushSubscriptionOptionsInit: base_config_1.TYPE,
    QueuingStrategy: base_config_1.TYPE,
    QueuingStrategyInit: base_config_1.TYPE,
    QueuingStrategySize: base_config_1.TYPE,
    ReadableByteStreamController: base_config_1.TYPE_VALUE,
    ReadableStream: base_config_1.TYPE_VALUE,
    ReadableStreamBYOBReader: base_config_1.TYPE_VALUE,
    ReadableStreamBYOBRequest: base_config_1.TYPE_VALUE,
    ReadableStreamController: base_config_1.TYPE,
    ReadableStreamDefaultController: base_config_1.TYPE_VALUE,
    ReadableStreamDefaultReader: base_config_1.TYPE_VALUE,
    ReadableStreamGenericReader: base_config_1.TYPE,
    ReadableStreamGetReaderOptions: base_config_1.TYPE,
    ReadableStreamIteratorOptions: base_config_1.TYPE,
    ReadableStreamReadDoneResult: base_config_1.TYPE,
    ReadableStreamReader: base_config_1.TYPE,
    ReadableStreamReaderMode: base_config_1.TYPE,
    ReadableStreamReadResult: base_config_1.TYPE,
    ReadableStreamReadValueResult: base_config_1.TYPE,
    ReadableStreamType: base_config_1.TYPE,
    ReadableWritablePair: base_config_1.TYPE,
    ReferrerPolicy: base_config_1.TYPE,
    RegistrationOptions: base_config_1.TYPE,
    Report: base_config_1.TYPE_VALUE,
    ReportBody: base_config_1.TYPE_VALUE,
    ReportingObserver: base_config_1.TYPE_VALUE,
    ReportingObserverCallback: base_config_1.TYPE,
    ReportingObserverOptions: base_config_1.TYPE,
    ReportList: base_config_1.TYPE,
    Request: base_config_1.TYPE_VALUE,
    RequestCache: base_config_1.TYPE,
    RequestCredentials: base_config_1.TYPE,
    RequestDestination: base_config_1.TYPE,
    RequestInfo: base_config_1.TYPE,
    RequestInit: base_config_1.TYPE,
    RequestMode: base_config_1.TYPE,
    RequestPriority: base_config_1.TYPE,
    RequestRedirect: base_config_1.TYPE,
    ResizeQuality: base_config_1.TYPE,
    Response: base_config_1.TYPE_VALUE,
    ResponseInit: base_config_1.TYPE,
    ResponseType: base_config_1.TYPE,
    RsaHashedImportParams: base_config_1.TYPE,
    RsaHashedKeyGenParams: base_config_1.TYPE,
    RsaKeyGenParams: base_config_1.TYPE,
    RsaOaepParams: base_config_1.TYPE,
    RsaOtherPrimesInfo: base_config_1.TYPE,
    RsaPssParams: base_config_1.TYPE,
    RTCEncodedAudioFrame: base_config_1.TYPE_VALUE,
    RTCEncodedAudioFrameMetadata: base_config_1.TYPE,
    RTCEncodedVideoFrame: base_config_1.TYPE_VALUE,
    RTCEncodedVideoFrameMetadata: base_config_1.TYPE,
    RTCEncodedVideoFrameType: base_config_1.TYPE,
    RTCRtpScriptTransformer: base_config_1.TYPE_VALUE,
    RTCTransformEvent: base_config_1.TYPE_VALUE,
    SecurityPolicyViolationEvent: base_config_1.TYPE_VALUE,
    SecurityPolicyViolationEventDisposition: base_config_1.TYPE,
    SecurityPolicyViolationEventInit: base_config_1.TYPE,
    ServiceWorker: base_config_1.TYPE_VALUE,
    ServiceWorkerContainer: base_config_1.TYPE_VALUE,
    ServiceWorkerContainerEventMap: base_config_1.TYPE,
    ServiceWorkerEventMap: base_config_1.TYPE,
    ServiceWorkerGlobalScope: base_config_1.TYPE_VALUE,
    ServiceWorkerGlobalScopeEventMap: base_config_1.TYPE,
    ServiceWorkerRegistration: base_config_1.TYPE_VALUE,
    ServiceWorkerRegistrationEventMap: base_config_1.TYPE,
    ServiceWorkerState: base_config_1.TYPE,
    ServiceWorkerUpdateViaCache: base_config_1.TYPE,
    SharedWorkerGlobalScope: base_config_1.TYPE_VALUE,
    SharedWorkerGlobalScopeEventMap: base_config_1.TYPE,
    StorageEstimate: base_config_1.TYPE,
    StorageManager: base_config_1.TYPE_VALUE,
    StreamPipeOptions: base_config_1.TYPE,
    StructuredSerializeOptions: base_config_1.TYPE,
    StylePropertyMapReadOnly: base_config_1.TYPE_VALUE,
    SubtleCrypto: base_config_1.TYPE_VALUE,
    TexImageSource: base_config_1.TYPE,
    TextDecodeOptions: base_config_1.TYPE,
    TextDecoder: base_config_1.TYPE_VALUE,
    TextDecoderCommon: base_config_1.TYPE,
    TextDecoderOptions: base_config_1.TYPE,
    TextDecoderStream: base_config_1.TYPE_VALUE,
    TextEncoder: base_config_1.TYPE_VALUE,
    TextEncoderCommon: base_config_1.TYPE,
    TextEncoderEncodeIntoResult: base_config_1.TYPE,
    TextEncoderStream: base_config_1.TYPE_VALUE,
    TextMetrics: base_config_1.TYPE_VALUE,
    TimerHandler: base_config_1.TYPE,
    Transferable: base_config_1.TYPE,
    TransferFunction: base_config_1.TYPE,
    Transformer: base_config_1.TYPE,
    TransformerFlushCallback: base_config_1.TYPE,
    TransformerStartCallback: base_config_1.TYPE,
    TransformerTransformCallback: base_config_1.TYPE,
    TransformStream: base_config_1.TYPE_VALUE,
    TransformStreamDefaultController: base_config_1.TYPE_VALUE,
    Uint32List: base_config_1.TYPE,
    UnderlyingByteSource: base_config_1.TYPE,
    UnderlyingDefaultSource: base_config_1.TYPE,
    UnderlyingSink: base_config_1.TYPE,
    UnderlyingSinkAbortCallback: base_config_1.TYPE,
    UnderlyingSinkCloseCallback: base_config_1.TYPE,
    UnderlyingSinkStartCallback: base_config_1.TYPE,
    UnderlyingSinkWriteCallback: base_config_1.TYPE,
    UnderlyingSource: base_config_1.TYPE,
    UnderlyingSourceCancelCallback: base_config_1.TYPE,
    UnderlyingSourcePullCallback: base_config_1.TYPE,
    UnderlyingSourceStartCallback: base_config_1.TYPE,
    URL: base_config_1.TYPE_VALUE,
    URLSearchParams: base_config_1.TYPE_VALUE,
    VideoColorPrimaries: base_config_1.TYPE,
    VideoColorSpace: base_config_1.TYPE_VALUE,
    VideoColorSpaceInit: base_config_1.TYPE,
    VideoConfiguration: base_config_1.TYPE,
    VideoDecoder: base_config_1.TYPE_VALUE,
    VideoDecoderConfig: base_config_1.TYPE,
    VideoDecoderEventMap: base_config_1.TYPE,
    VideoDecoderInit: base_config_1.TYPE,
    VideoDecoderSupport: base_config_1.TYPE,
    VideoEncoder: base_config_1.TYPE_VALUE,
    VideoEncoderBitrateMode: base_config_1.TYPE,
    VideoEncoderConfig: base_config_1.TYPE,
    VideoEncoderEncodeOptions: base_config_1.TYPE,
    VideoEncoderEventMap: base_config_1.TYPE,
    VideoEncoderInit: base_config_1.TYPE,
    VideoEncoderSupport: base_config_1.TYPE,
    VideoFrame: base_config_1.TYPE_VALUE,
    VideoFrameBufferInit: base_config_1.TYPE,
    VideoFrameCopyToOptions: base_config_1.TYPE,
    VideoFrameInit: base_config_1.TYPE,
    VideoFrameOutputCallback: base_config_1.TYPE,
    VideoMatrixCoefficients: base_config_1.TYPE,
    VideoPixelFormat: base_config_1.TYPE,
    VideoTransferCharacteristics: base_config_1.TYPE,
    VoidFunction: base_config_1.TYPE,
    WebAssembly: base_config_1.TYPE_VALUE,
    WebCodecsErrorCallback: base_config_1.TYPE,
    WEBGL_color_buffer_float: base_config_1.TYPE,
    WEBGL_compressed_texture_astc: base_config_1.TYPE,
    WEBGL_compressed_texture_etc: base_config_1.TYPE,
    WEBGL_compressed_texture_etc1: base_config_1.TYPE,
    WEBGL_compressed_texture_pvrtc: base_config_1.TYPE,
    WEBGL_compressed_texture_s3tc: base_config_1.TYPE,
    WEBGL_compressed_texture_s3tc_srgb: base_config_1.TYPE,
    WEBGL_debug_renderer_info: base_config_1.TYPE,
    WEBGL_debug_shaders: base_config_1.TYPE,
    WEBGL_depth_texture: base_config_1.TYPE,
    WEBGL_draw_buffers: base_config_1.TYPE,
    WEBGL_lose_context: base_config_1.TYPE,
    WEBGL_multi_draw: base_config_1.TYPE,
    WebGL2RenderingContext: base_config_1.TYPE_VALUE,
    WebGL2RenderingContextBase: base_config_1.TYPE,
    WebGL2RenderingContextOverloads: base_config_1.TYPE,
    WebGLActiveInfo: base_config_1.TYPE_VALUE,
    WebGLBuffer: base_config_1.TYPE_VALUE,
    WebGLContextAttributes: base_config_1.TYPE,
    WebGLContextEvent: base_config_1.TYPE_VALUE,
    WebGLContextEventInit: base_config_1.TYPE,
    WebGLFramebuffer: base_config_1.TYPE_VALUE,
    WebGLPowerPreference: base_config_1.TYPE,
    WebGLProgram: base_config_1.TYPE_VALUE,
    WebGLQuery: base_config_1.TYPE_VALUE,
    WebGLRenderbuffer: base_config_1.TYPE_VALUE,
    WebGLRenderingContext: base_config_1.TYPE_VALUE,
    WebGLRenderingContextBase: base_config_1.TYPE,
    WebGLRenderingContextOverloads: base_config_1.TYPE,
    WebGLSampler: base_config_1.TYPE_VALUE,
    WebGLShader: base_config_1.TYPE_VALUE,
    WebGLShaderPrecisionFormat: base_config_1.TYPE_VALUE,
    WebGLSync: base_config_1.TYPE_VALUE,
    WebGLTexture: base_config_1.TYPE_VALUE,
    WebGLTransformFeedback: base_config_1.TYPE_VALUE,
    WebGLUniformLocation: base_config_1.TYPE_VALUE,
    WebGLVertexArrayObject: base_config_1.TYPE_VALUE,
    WebGLVertexArrayObjectOES: base_config_1.TYPE,
    WebSocket: base_config_1.TYPE_VALUE,
    WebSocketEventMap: base_config_1.TYPE,
    WebTransport: base_config_1.TYPE_VALUE,
    WebTransportBidirectionalStream: base_config_1.TYPE_VALUE,
    WebTransportCloseInfo: base_config_1.TYPE,
    WebTransportCongestionControl: base_config_1.TYPE,
    WebTransportDatagramDuplexStream: base_config_1.TYPE_VALUE,
    WebTransportError: base_config_1.TYPE_VALUE,
    WebTransportErrorOptions: base_config_1.TYPE,
    WebTransportErrorSource: base_config_1.TYPE,
    WebTransportHash: base_config_1.TYPE,
    WebTransportOptions: base_config_1.TYPE,
    WebTransportSendStreamOptions: base_config_1.TYPE,
    WindowClient: base_config_1.TYPE_VALUE,
    WindowOrWorkerGlobalScope: base_config_1.TYPE,
    Worker: base_config_1.TYPE_VALUE,
    WorkerEventMap: base_config_1.TYPE,
    WorkerGlobalScope: base_config_1.TYPE_VALUE,
    WorkerGlobalScopeEventMap: base_config_1.TYPE,
    WorkerLocation: base_config_1.TYPE_VALUE,
    WorkerNavigator: base_config_1.TYPE_VALUE,
    WorkerOptions: base_config_1.TYPE,
    WorkerType: base_config_1.TYPE,
    WritableStream: base_config_1.TYPE_VALUE,
    WritableStreamDefaultController: base_config_1.TYPE_VALUE,
    WritableStreamDefaultWriter: base_config_1.TYPE_VALUE,
    WriteCommandType: base_config_1.TYPE,
    WriteParams: base_config_1.TYPE,
    XMLHttpRequest: base_config_1.TYPE_VALUE,
    XMLHttpRequestBodyInit: base_config_1.TYPE,
    XMLHttpRequestEventMap: base_config_1.TYPE,
    XMLHttpRequestEventTarget: base_config_1.TYPE_VALUE,
    XMLHttpRequestEventTargetEventMap: base_config_1.TYPE,
    XMLHttpRequestResponseType: base_config_1.TYPE,
    XMLHttpRequestUpload: base_config_1.TYPE_VALUE,
};
//# sourceMappingURL=webworker.js.map