//@ts-nocheck
import { Injectable, Store } from "bwtk";

import { Observable } from "rxjs";
import { combineEpics } from "redux-observable";
import { IGetRedirectUrlEpicResponse, IGetRedirectUrlEpic, IInteracBankInfoEpic, IInteracBankInfoEpicResponse, ICancelPreauthPaymentsEpic, ICancelPreauthPaymentsEpicResponse } from "../models";
import {
  tokenizeAndPropagateFormValues,
  getPassKey,
  setPassKey,
  cardTokenizationSuccess,
  // setCreditCardInfo,
  cardTokenizationError,
  getRedirectUrl,
  redirectUrlFailure,
  redirectUrlSuccess,
  getInteracBankInfo,
  interacBankInfoSuccess,
  interacBankInfoFailure,
  setIsLoading,
  createMultiPaymentAction,
  createMultiPaymentCompleted,
  createMultiPaymentFailed,
  validateMultiOrderPaymentAction,
  validateMultiOrderPaymentActionCompleted,
  validateMultiOrderPaymentActionFailed,
  submitMultiOrderPaymentAction,
  submitMultiOrderPaymentActionCompleted,
  submitMultiOrderPaymentActionFailed,
  cancelPreauthAction,
  cancelPreauthSuccessAction,
  cancelPreauthFailureAction
} from "./Actions";

import Tokenize, {
  IDTSTokenizationPluginResponse
} from "../utils/tokenize";

// import Config from "../Config";
import { Client } from "../Client";
import Config from "../Config";


@Injectable
export class EpicRoot {
  constructor(private client: Client, private config: Config) { }
  combineEpics() {
    return combineEpics(
      this.createMultiPaymentEpic,
      this.validateMultiOrderPaymentEpic,
      this.submitMultiOrderPaymentEpic,
      this.tokenizeAndPropagateFormValues,
      this.fetchPassKey,
      this.getRedirectUrl,
      this.getInteracBankInfo,
      this.cancelPreauthPaymentsEpic,
    );
  }

  private get tokenizeAndPropagateFormValues() {
    return (action$: any, store: Store) =>
      action$.ofType(tokenizeAndPropagateFormValues.toString())
        // Set the widget staus to loading
        .mergeMap((action: any) =>
          Tokenize("card-number", this.config.DTSTokenization, store.getState().passKey)
            .mergeMap((response: IDTSTokenizationPluginResponse) => {
              return [
                getPassKey({ ban: action.payload.BillName, sub: action.payload.subscriberId }),
                cardTokenizationSuccess(response.token)
              ]
            })
            .catch((err) => {
              return [
                cardTokenizationError(typeof (err) === "string" && err.length > 0 ? err : "TOKENIZATIONERROR"),
              ]
            }),

        )
        .catch((err: any) => [
          cardTokenizationError("TOKENIZATIONERROR"),
        ]);
  }

  private get fetchPassKey() {
    // const { options } = this.client;
    return (action$: any, store: Store) =>
      action$.ofType(getPassKey.toString())
        // Set the widget staus to loading
        .mergeMap((action: any) =>
          this.client.getPassKeyRepsonse(action)
            .map((response: any) => {
              return setPassKey(response?.data?.PassKey);
            }
            )
        )
        .catch((err: any) => [
          cardTokenizationError("TOKENIZATIONERROR")
        ]);
  }

  private get createMultiPaymentEpic(): any {
    return (action$: any, store: Store) => {
      return action$.ofType(createMultiPaymentAction.toString())
        .mergeMap(({ payload }: ReduxActions.Action<{ ban: string, type: boolean, details: any, sub?: string | null }>) =>
          this.client.createMultiOrderFormData(payload?.ban, payload?.type, payload?.details, payload?.sub)
            .map(({ data }: any) => {
              return createMultiPaymentCompleted(data);
            })
            .catch((error: any) => {
              return Observable.of({ ...createMultiPaymentFailed(error), error: true });
            })
        );
    };
  }

  private get validateMultiOrderPaymentEpic(): any {
    return (action$: any, store: Store) => {
      return action$.ofType(validateMultiOrderPaymentAction.toString())
        .mergeMap(({ payload }: ReduxActions.Action<{ ban: string, type: boolean, details: any, accountInputValue: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined }>) =>
          this.client.validateMultiOrderForm(payload?.ban, payload?.type, payload?.details, payload?.accountInputValue, payload?.isBankPaymentSelected, payload?.sub, store.getState().cardTokenizationSuccess)
            .map(({ data }: any) => {
              return validateMultiOrderPaymentActionCompleted(data);
            })
            .catch((error: any) => {
              return Observable.of({ ...validateMultiOrderPaymentActionFailed(error), error: true });
            })
        );
    };
  }

  private get submitMultiOrderPaymentEpic(): any {
    return (action$: any, store: Store) => {
      return action$.ofType(submitMultiOrderPaymentAction.toString())
        .mergeMap(({ payload }: ReduxActions.Action<{
          ban: string,
          type: boolean,
          isbankSelected: boolean,
          sorryCredit: boolean,
          sorryDebit: boolean,
          details: any,
          sub?: string | null
        }>) =>
          this.client.submitMultiOrderForm(payload?.ban, payload?.type, payload?.isbankSelected, payload?.sorryCredit, payload?.sorryDebit, payload?.details, payload?.sub)
            .map(({ data }: any) => {
              if (data.length > 0) {
                let mutliOrderAllSuccess = data.find((currentElement) => currentElement.OrderFormStatus == 'Confirmation');
                if (mutliOrderAllSuccess) {
                  return submitMultiOrderPaymentActionCompleted(data);
                }
              }
              return submitMultiOrderPaymentActionFailed({ error: true });
            })
            .catch((error: any) => {
              return Observable.of({ ...submitMultiOrderPaymentActionFailed(error), error: true });
            })
        );
    };
  }

  private get getRedirectUrl(): IGetRedirectUrlEpic {
    return (action$: any, store: Store) => {
      return action$.ofType(getRedirectUrl.toString())
        .mergeMap((action: any) =>
          this.client.getRedirectUrl()
            .map(({ data }: IGetRedirectUrlEpicResponse) => {
              return redirectUrlSuccess(data);
            })
            .catch((error: any) => {
              return Observable.of({ ...redirectUrlFailure(error), error: true });
            })
        );
    };
  }

  private get getInteracBankInfo(): IInteracBankInfoEpic {
    return (action$: any, store: Store) => {
      return action$.ofType(getInteracBankInfo.toString())
        .mergeMap(({ payload }: ReduxActions.Action<{ code: string }>) => {
          store.dispatch(setIsLoading(true));
          return this.client.getInteracBankInfo(payload?.code)
            .map(({ data }: IInteracBankInfoEpicResponse) => {
              store.dispatch(setIsLoading(false));
              return interacBankInfoSuccess(data);
            })
            .catch((error: any) => {
              store.dispatch(setIsLoading(false));
              return Observable.of({ ...interacBankInfoFailure(error), error: true });
            })
        });
    };
  }

  private get cancelPreauthPaymentsEpic(): ICancelPreauthPayments {
    return (action$: any, store: Store) => {
      return action$.ofType(cancelPreauthAction.toString())
        .mergeMap(({ payload }: ReduxActions.Action<{ bans: string[] }>) => {
          store.dispatch(setIsLoading(true));
          return this.client.cancelPreauth(payload?.bans)
            .map(({ data }: ICancelPreauthPaymentsEpicResponse) => {
              if (data.length > 0) {
                const successCancelPreauth = data.filter(item => item.success);
                if (successCancelPreauth && successCancelPreauth.length > 0) {
                  store.dispatch(setIsLoading(false));
                  return cancelPreauthSuccessAction(data);
                }
              }
              store.dispatch(setIsLoading(false));
              return cancelPreauthFailureAction({ error: true });
            })
            .catch((error: any) => {
              store.dispatch(setIsLoading(false));
              return Observable.of({ ...cancelPreauthFailureAction(error), error: true });
            })
        });
    };
  }
}

