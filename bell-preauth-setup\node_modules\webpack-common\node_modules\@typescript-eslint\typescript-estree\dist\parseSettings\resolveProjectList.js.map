{"version": 3, "file": "resolveProjectList.js", "sourceRoot": "", "sources": ["../../src/parseSettings/resolveProjectList.ts"], "names": [], "mappings": ";;;;;AA0BA,wCAEC;AAKD,gDAwFC;AA2BD,4DAGC;AAvJD,kDAA0B;AAC1B,yCAA6C;AAC7C,sDAA6B;AAK7B,qDAIkC;AAClC,mDAGyB;AAEzB,MAAM,GAAG,GAAG,IAAA,eAAK,EACf,6EAA6E,CAC9E,CAAC;AAEF,IAAI,gBAAgB,GAGT,IAAI,CAAC;AAEhB,SAAgB,cAAc;IAC5B,gBAAgB,EAAE,KAAK,EAAE,CAAC;AAC5B,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAChC,OAME;IAEF,MAAM,iBAAiB,GAAa,EAAE,CAAC;IAEvC,2CAA2C;IAC3C,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;QAC5B,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACtC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAChC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnC,OAAO,IAAI,GAAG,EAAE,CAAC;IACnB,CAAC;IAED,MAAM,uBAAuB,GAAG,CAC9B,OAAO,CAAC,uBAAuB,IAAI,CAAC,oBAAoB,CAAC,CAC1D;SACE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC;QAC7C,qCAAqC;SACpC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,CAAC,CAAC;IAEnE,MAAM,QAAQ,GAAG,OAAO,CAAC;QACvB,OAAO,EAAE,iBAAiB;QAC1B,uBAAuB;QACvB,eAAe,EAAE,OAAO,CAAC,eAAe;KACzC,CAAC,CAAC;IACH,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;QAC7B,gFAAgF;QAChF,8EAA8E;QAC9E,8EAA8E;QAC9E,8EAA8E;QAC9E,gBAAgB,GAAG,IAAI,6BAAa,CAClC,OAAO,CAAC,SAAS;YACf,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI;gBAC3B,uDAAuC,CAC5C,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED,qCAAqC;IACrC,MAAM,eAAe,GAAG,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAA,iBAAM,EAAC,OAAO,CAAC,CAAC,CAAC;IAC9E,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,IAAA,iBAAM,EAAC,OAAO,CAAC,CAAC,CAAC;IAE1E,IAAI,gBAAgB,GAAa,EAAE,CAAC;IAEpC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC5B,8FAA8F;QAC9F,qGAAqG;QACrG,kEAAkE;QAClE,gBAAgB,GAAG,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAChD,IAAA,gBAAQ,EAAC,OAAO,EAAE;YAChB,GAAG,EAAE,OAAO,CAAC,eAAe;YAC5B,MAAM,EAAE,uBAAuB;SAChC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,MAAM,2BAA2B,GAAG,IAAI,GAAG,CACzC,CAAC,GAAG,eAAe,EAAE,GAAG,gBAAgB,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;QACvD,IAAA,6BAAoB,EAClB,IAAA,2BAAkB,EAAC,OAAO,EAAE,OAAO,CAAC,eAAe,CAAC,CACrD;QACD,IAAA,2BAAkB,EAAC,OAAO,EAAE,OAAO,CAAC,eAAe,CAAC;KACrD,CAAC,CACH,CAAC;IAEF,GAAG,CACD,gEAAgE,EAChE,2BAA2B,CAC5B,CAAC;IAEF,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,2BAA2B,CAAC,CAAC;IAC5D,OAAO,2BAA2B,CAAC;AACrC,CAAC;AAED,SAAS,OAAO,CAAC,EACf,OAAO,EACP,uBAAuB,EACvB,eAAe,GAKf;IACA,+CAA+C;IAC/C,MAAM,UAAU,GAAG;QACjB,eAAe;QACf,kEAAkE;QAClE,OAAO;QACP,6CAA6C;QAC7C,uBAAuB,EAAE,CAAC,GAAG,uBAAuB,CAAC,CAAC,IAAI,EAAE;KAC7D,CAAC;IAEF,OAAO,IAAA,mBAAU,EAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAChD,CAAC;AAED;;;GAGG;AACH,SAAgB,wBAAwB;IACtC,gBAAgB,EAAE,KAAK,EAAE,CAAC;IAC1B,gBAAgB,GAAG,IAAI,CAAC;AAC1B,CAAC"}