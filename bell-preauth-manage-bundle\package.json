{"name": "bell-preauth-manage-bundle", "version": "1.0.0", "domain": "Payments", "private": true, "scripts": {"linklocal": "linklocal", "dev": "webpack -w", "build": "webpack", "start": "http-server", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"bwtk": "git+https://gitlab.int.bell.ca/uxp/bwtk.git", "bell-preauth-manage": "file:../bell-preauth-manage", "redux-observable": "^0.19.0", "husky": "4.3.8", "webpack-cli": "^5.1.4"}, "peerDependencies": {"bwtk-polyfill": "*", "bwtk": "*", "react": "*", "react-dom": "*", "react-intl": "*", "react-redux": "*", "rxjs": "*", "redux": "*", "redux-actions": "*", "redux-observable": "*", "prop-types": "*", "scarlet": "*"}, "husky": {"hooks": {"pre-commit": "node pre-commit.js"}}}