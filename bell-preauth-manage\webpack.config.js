const { widgetTemplate } = require("webpack-common");
const package = require("./package.json");
const path = require("path");


module.exports = (env) => {
    const config = widgetTemplate(
      package,
      {
        widget: path.resolve(__dirname, "src"),
        node_modules: path.resolve(__dirname, "node_modules"),
        dist: path.resolve(__dirname, "dist"),
        assetModuleFilename: "/Styles/BRF3/content/fonts/[name][ext]"
      },
      {
        mode: env["-p"] === true ? "production" : "none",
        stats: {
          errorDetails: !env["-p"]
        }
      }
    );
    config.forEach(def => {
      def.module.rules.forEach(rule => {
        if(rule.use) {
          const cssloader = rule.use.find(mod => mod.loader === "css-loader");
          if(cssloader) cssloader.options = {};
        }
      });
      def.module.rules.push({
        test: /\.(eot|woff|woff2|svg|ttf)([?]?.*)$/,
        type: "asset/resource",
        generator: {
          filename: "Styles/BRF3/content/fonts/[name][ext]"
        }
      });
    });
    return config;
  };
  