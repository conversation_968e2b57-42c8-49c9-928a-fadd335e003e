// @ts-nocheck
import { Injectable, Store } from "bwtk";

import { Observable, of, from, catchError, map, mergeMap } from "rxjs";
import { combineEpics, ofType } from "redux-observable";
import { IGetRedirectUrlEpicResponse, IGetRedirectUrlEpic, IInteracBankInfoEpic, IInteracBankInfoEpicResponse } from "../models";
import {
  tokenizeAndPropagateFormValues,
  getPassKey,
  setPassKey,
  cardTokenizationSuccess,
  // setCreditCardInfo,
  cardTokenizationError,
  getRedirectUrl,
  redirectUrlFailure,
  redirectUrlSuccess,
  getInteracBankInfo,
  interacBankInfoSuccess,
  interacBankInfoFailure,
  setIsLoading,
  createMultiPaymentAction,
  createMultiPaymentCompleted,
  createMultiPaymentFailed,
  validateMultiOrderPaymentAction,
  validateMultiOrderPaymentActionCompleted,
  validateMultiOrderPaymentActionFailed,
  submitMultiOrderPaymentAction,
  submitMultiOrderPaymentActionCompleted,
  submitMultiOrderPaymentActionFailed,
  validateOrderPaymentAction,
  validateOrderPaymentActionCompleted,
  validateOrderPaymentActionFailed,
  createPaymentAction,
  createPaymentCompleted,
  createPaymentFailed,
  submitOrderPaymentAction,
  submitOrderPaymentActionCompleted,
  submitOrderPaymentActionFailed,
} from "./Actions";

import Tokenize, {
  IDTSTokenizationPluginResponse
} from "../utils/tokenize";

// import Config from "../Config";
import { Client } from "../Client";
import Config from "../Config";

// Add ofType operator for redux-observable
declare module "rxjs/Observable" {
  interface Observable<T> {
    ofType(type: string): Observable<T>;
  }
}


@Injectable
export class EpicRoot {
  constructor(private client: Client, private config: Config) { }
  combineEpics() {
    return combineEpics(
      //  this.fetchPreAuthorizedPaymentEpic,
      this.createPaymentEpic,
      this.validateOrderPaymentEpic,
      this.submitOrderPaymentEpic,
      this.createMultiPaymentEpic,
      this.validateMultiOrderPaymentEpic,
      this.submitMultiOrderPaymentEpic,
      this.tokenizeAndPropagateFormValues,
      this.fetchPassKey,
      this.getRedirectUrl,
      this.getInteracBankInfo
    );
  }

  // private get fetchPreAuthorizedPaymentEpic(): any {
  //     // let { options } = this.client;
  //     return (action$: any, store: any) => 
  //         action$.ofType(fetchPaymentItems.toString()).pipe(
  //             // mergeMap((action: ReduxActions.Action<IPreAuthorizedPayment>) => 
  //             //     this.client.get<any>(options.preAuthorizedPayment).pipe(
  //             //         mergeMap((reply) => getResponseData(reply)) 
  //             //     )
  //             // ),
  //             mergeMap((response: any) => of(setPaymentItems(response))),
  //             catchError((error: any) => {
  //                 console.log(error);
  //                 return of({...fetchPaymentItemsFailed(error), error: true});
  //             })
  //         );
  // }

  private get tokenizeAndPropagateFormValues() {
    return (action$: any, store: Store) =>
      action$.pipe(
        ofType(tokenizeAndPropagateFormValues.toString()),
        mergeMap((action: any) =>
          Tokenize("card-number", this.config.DTSTokenization, store.value.passKey).pipe(
            mergeMap((response: IDTSTokenizationPluginResponse) => [
              getPassKey({ ban: action.payload.BillName, sub: action.payload.subscriberId }),
              cardTokenizationSuccess(response.token)
            ]),
            catchError((err) => of(
              cardTokenizationError(typeof (err) === "string" && err.length > 0 ? err : "TOKENIZATIONERROR")
            ))
          )
        ),
        catchError((err: any) => of(
          cardTokenizationError("TOKENIZATIONERROR")
        ))
      );
  }

  private get fetchPassKey() {
    // const { options } = this.client;
    return (action$: any, store: Store) =>
      action$
        .ofType(getPassKey.toString())
        // Set the widget staus to loading
        .mergeMap((action: any) =>
          this.client.getPassKeyRepsonse(action)
            .map((response: any) => setPassKey(response?.data?.PassKey))
        )
        .catch((err: any) => Observable.of(
          cardTokenizationError("TOKENIZATIONERROR")
        ));
  }

  private get createMultiPaymentEpic(): any {
    return (action$: any, store: Store) => action$
      .ofType(createMultiPaymentAction.toString())
      .mergeMap(({ payload }: ReduxActions.Action<{ ban: string, type: boolean, details: any, sub?: string | null }>) =>
        this.client.createMultiOrderFormData(payload?.ban, payload?.type, payload?.details, payload?.sub)
          .map(({ data }: any) => createMultiPaymentCompleted(data))
          .catch((error: any) => Observable.of({ ...createMultiPaymentFailed(error), error: true }))
      );
  }

  private get validateMultiOrderPaymentEpic(): any {
    return (action$: any, store: Store) => action$
      .ofType(validateMultiOrderPaymentAction.toString())
      .mergeMap(({ payload }: ReduxActions.Action<{ ban: string, type: boolean, details: any, accountInputValue: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined }>) =>
        this.client.validateMultiOrderForm(payload?.ban, payload?.type, payload?.details, payload?.accountInputValue, payload?.isBankPaymentSelected, payload?.sub, store.value.cardTokenizationSuccess)
          .map(({ data }: any) => validateMultiOrderPaymentActionCompleted(data))
          .catch((error: any) => Observable.of({ ...validateMultiOrderPaymentActionFailed(error), error: true }))
      );
  }

  private get submitMultiOrderPaymentEpic(): any {
    return (action$: any, store: Store) =>
      action$.pipe(
        ofType(submitMultiOrderPaymentAction.toString()),
        mergeMap(({ payload }: ReduxActions.Action<{ ban: string, type: boolean, isbankSelected: boolean, sorryCredit: boolean, sorryDebit: boolean, details: any, sub?: string | null }>) =>
          from(this.client.submitMultiOrderForm(payload?.ban, payload?.type, payload?.isbankSelected, payload?.sorryCredit, payload?.sorryDebit, payload?.details, payload?.sub)).pipe(
            map(({ data }: any) => {
              if (data.length > 0) {
                const mutliOrderAllSuccess = data.find((currentElement: any) => currentElement.OrderFormStatus === 'Confirmation');
                if (mutliOrderAllSuccess) {
                  return submitMultiOrderPaymentActionCompleted(data);
                }
              }
              return submitMultiOrderPaymentActionFailed({ error: true });
            }),
            catchError((error: any) => of({ ...submitMultiOrderPaymentActionFailed(error), error: true }))
          )
        )
      );
  }

  private get getRedirectUrl(): IGetRedirectUrlEpic {
    return (action$: any, store: Store) => action$
      .ofType(getRedirectUrl.toString())
      .mergeMap((action: any) =>
        this.client.getRedirectUrl()
          .map(({ data }: IGetRedirectUrlEpicResponse) => redirectUrlSuccess(data))
          .catch((error: any) => Observable.of({ ...redirectUrlFailure(error), error: true }))
      );
  }

  private get getInteracBankInfo(): IInteracBankInfoEpic {
    return (action$: any, store: Store) => action$
      .ofType(getInteracBankInfo.toString())
      .mergeMap(({ payload }: ReduxActions.Action<{ code: string }>) => {
        store.dispatch(setIsLoading(true));
        return this.client.getInteracBankInfo(payload?.code)
          .map(({ data }: IInteracBankInfoEpicResponse) => {
            store.dispatch(setIsLoading(false));
            return interacBankInfoSuccess(data);
          })
          .catch((error: any) => {
            store.dispatch(setIsLoading(false));
            return Observable.of({ ...interacBankInfoFailure(error), error: true });
          });
      });
  }

  private get createPaymentEpic(): any {
    return (action$: any, store: Store) => action$
      .ofType(createPaymentAction.toString())
      .mergeMap(({ payload }: ReduxActions.Action<{ ban: string, type: boolean, sub?: string | null }>) =>
        this.client.createOrderFormData(payload?.ban, payload?.type, payload?.sub)
          .map(({ data }: any) => createPaymentCompleted(data))
          .catch((error: any) => Observable.of({ ...createPaymentFailed(error), error: true }))
      );
  }

  private get validateOrderPaymentEpic(): any {
    return (action$: any, store: Store) => action$
      .ofType(validateOrderPaymentAction.toString())
      .mergeMap(({ payload }: ReduxActions.Action<{ ban: string, type: boolean, details: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined }>) =>
        this.client.validateOrderForm(payload?.ban, payload?.type, payload?.details, payload?.isBankPaymentSelected, payload?.sub, store.value.cardTokenizationSuccess)
          .map(({ data }: any) => validateOrderPaymentActionCompleted(data))
          .catch((error: any) => Observable.of({ ...validateOrderPaymentActionFailed(error), error: true }))
      );
  }

  private get submitOrderPaymentEpic(): any {
    return (action$: any, store: Store) => action$
      .ofType(submitOrderPaymentAction.toString())
      .mergeMap(({ payload }: ReduxActions.Action<{ ban: string, type: boolean, sub?: string | null }>) =>
        this.client.submitOrderForm(payload?.ban, payload?.type, payload?.sub)
          .map(({ data }: any) => submitOrderPaymentActionCompleted(data))
          .catch((error: any) => Observable.of({ ...submitOrderPaymentActionFailed(error), error: true }))
      );
  }

}

