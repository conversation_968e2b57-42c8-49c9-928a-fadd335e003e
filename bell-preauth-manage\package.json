{"name": "bell-preauth-manage", "version": "1.0.0", "description": "", "main": "dist/widget.js", "private": true, "scripts": {"linklocal": "linklocal", "dev": "webpack -w", "build": "npm run build:tailwind && webpack", "test": "echo \"Error: no test specified\" && exit 1", "start": "http-server --cors ./", "build:tailwind": "npx tailwindcss build -i ./src/css/styles.css -o ./src/css/bell-payment-flow-manage.css --minify", "watch:tailwind": "npx tailwindcss -i ./src/css/styles.css -o ./src/css/bell-payment-flow.css --watch"}, "keywords": [], "author": "BELL", "license": "MIT", "devDependencies": {"@bell/bell-ui-library": "3.4.3", "bwtk": "git+https://gitlab.int.bell.ca/uxp/bwtk", "bell-preauth-common": "file:../bell-preauth-common", "husky": "4.3.8", "webpack-cli": "^5.1.4", "@types/react-router-dom": "*", "css-loader": "^6.2.0", "prettier": "2.3.2", "tailwindcss": "^3.4.1"}, "peerDependencies": {"bwtk": "*", "bwtk-polyfill": "*", "prop-types": "*", "react": "*", "react-bootsrap": "*", "react-dom": "*", "react-intl": "*", "react-redux": "*", "redux": "*", "redux-actions": "*", "redux-observable": "*", "rxjs": "*", "scarlet": "*"}, "husky": {"hooks": {"pre-commit": "node pre-commit.js"}}, "dependencies": {"typescript": "^4.8.2", "redux-observable": "0.18.0", "rxjs": "5.5.12"}}