import * as React from "react";
import { Provider } from "react-redux";
import { Widget, ViewWidget, Lo<PERSON>, LoggerConfigKeys } from "bwtk";
import Store from "./store";
import { App } from "./App";
import Config from "./Config";
import { createMultiPaymentAction, getPassKey } from "./store/Actions";
import { AccountInputValues, PaymentItemAccountType } from "./models";
import { getBanSpecificTransactionId } from "./utils";
import { Root } from "react-dom/client";

// Error Boundary for React 18 compatibility
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Widget Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', color: '#d32f2f', border: '1px solid #d32f2f', borderRadius: '4px', backgroundColor: '#ffeaea' }}>
          <h3>Something went wrong with the payment widget.</h3>
          <p>Please refresh the page to try again.</p>
        </div>
      );
    }

    return this.props.children;
  }
}

@Widget({ namespace: "Preauth/Setup" })
export default class extends ViewWidget {
  constructor
  (private store: Store,
    private config: Config,
    logger: Logger) {
    super();
    // Suppress unused parameter warning
    void logger;
  }

  init() {
    this.config.setConfig(LoggerConfigKeys.SeverityLevel, this.config.logLevel);

    // Add meta tags for external scripts that depend on them - do this after a short delay
    setTimeout(() => {
      this.addMetaTags();
    }, 0);
    // this.store.dispatch(setEnvironmentVariables({ ...this.config.environmentVariables, language: this.config.language }));
    // this.store.dispatch(widgetStatus(WidgetStates.INITIALIZED));
    // this.store.dispatch(fetchPaymentItems({}));
    // this.store.dispatch(fetchPaymentItems({ ...this.config, language: this.config.language}))
    // this.store.dispatch(getRedirectUrl({}));
    this.store.dispatch(getPassKey({ ban: this.config.getPaymentItem[0].Ban, sub: this.config.getPaymentItem[0].subscriberId }));
    if (this.config.getPaymentItem != null && this.config.getPaymentItem.length === 1) {
      const paymentItem = this.config.getPaymentItem[0];
      const accountInputValue: AccountInputValues[] = [{
        accountNumber: paymentItem.Ban,
        subNumber: paymentItem.subscriberId, // This can be null or undefined if not provided
        transactionID: getBanSpecificTransactionId(paymentItem.Ban, this.config.transactionIdArray),
        payBalanceAmnt: 0,
      }];
      this.store.dispatch(createMultiPaymentAction({ ban: paymentItem.Ban, type: (paymentItem.AccountType === PaymentItemAccountType.OneBill), details: accountInputValue, sub: paymentItem.subscriberId }));
    }
  }

  private addMetaTags() {
    // Add language meta tag if it doesn't exist
    if (!document.querySelector('meta[name="language"]')) {
      const languageMeta = document.createElement('meta');
      languageMeta.name = 'language';
      languageMeta.content = this.config.language || 'en';
      document.head.appendChild(languageMeta);
    }

    // Add province meta tag if it doesn't exist
    if (!document.querySelector('meta[name="province"]')) {
      const provinceMeta = document.createElement('meta');
      provinceMeta.name = 'province';
      provinceMeta.content = this.config.province || 'ON';
      document.head.appendChild(provinceMeta);
    }
  }


  destroy() {
    // Clean up meta tags we added
    this.removeMetaTags();
    this.store.destroy();
  }

  private removeMetaTags() {
    // Remove language meta tag if we added it
    const languageMeta = document.querySelector('meta[name="language"]');
    if (languageMeta && (languageMeta as HTMLMetaElement).content === (this.config.language || 'en')) {
      languageMeta.remove();
    }

    // Remove province meta tag if we added it
    const provinceMeta = document.querySelector('meta[name="province"]');
    if (provinceMeta && (provinceMeta as HTMLMetaElement).content === (this.config.province || 'ON')) {
      provinceMeta.remove();
    }
  }

  render(root: Root) {
    const { store, config } = this;
    try {
      root.render(
        <React.StrictMode>
          <ErrorBoundary>
            <Provider store={store}>
              <App Config={config} />
            </Provider>
          </ErrorBoundary>
        </React.StrictMode>
      );
    } catch (error) {
      console.error('Error rendering widget:', error);
      // Fallback: try to render a simple error message
      try {
        root.render(
          <div style={{ padding: '20px', color: '#d32f2f', border: '1px solid #d32f2f', borderRadius: '4px', backgroundColor: '#ffeaea' }}>
            Widget failed to load. Please refresh the page.
          </div>
        );
      } catch (fallbackError) {
        console.error('Fallback render also failed:', fallbackError);
      }
    }
  }
}

