import * as React from "react";
import { createRoot } from "react-dom/client";
import { WidgetLoader, Init } from "bwtk";


export function initialize(config: any, containerId: string, debug: any) {
  const updatedConfig = {
    ...config, "loader.staticWidgetMappings": {
      "bell-preauth-setup": {
        factory: () => require("bell-preauth-setup"),
        namespace: "Preauth/Setup"
      }
    }
  };
  Init(updatedConfig);

  const container = document.getElementById(containerId);
  if (container) {
    try {
      const reactRoot = createRoot(container);
      reactRoot.render(<WidgetLoader widget="bell-preauth-setup" />);
    } catch (error) {
      console.error('Error initializing bell-preauth-setup widget:', error);
      // Fallback: show error message in container
      container.innerHTML = '<div>Failed to load payment widget. Please refresh the page.</div>';
    }
  } else {
    console.error(`Container element with id "${containerId}" not found`);
  }
}
