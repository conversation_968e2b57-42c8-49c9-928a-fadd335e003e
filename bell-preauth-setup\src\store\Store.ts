import { combineReducers, Reducer } from "redux";
import { handleActions } from "redux-actions";
import { Store as BwtkStore, Injectable, CommonFeatures, LocalizationState } from "bwtk";
import { Localization } from "../Localization";
import Config from "../Config";
import * as actions from "./Actions";
import { IRequestStatus } from "../models/App";
import { createEpicMiddleware } from "redux-observable";
import {
  setConfigReducer,
  getConfigReducer,
  onCardHolderNameChangeReducer,
  onSecurityCodeChangeReducer,
  onCreditCardNumberChangeReducer,
  onCreditCardExpiryDateChangeReducer,
  setValidationErrorsReducer,
  setcreatePaymentReducer,
  setcreatePaymentStatusReducer,
  setValidateOrderPaymentReducer,
  setValidateOrderPaymentStatusReducer,
  setsubmitOrderPaymentReducer,
  setsubmitOrderPaymentStatusReducer,
  setPasskeyReducer,
  setTokenizeAndPropagateFormValuesStatusReducer,
  setGetRedirectUrlReducer,
  setInteracBankInfoReducer,
  setIsLoadingReducer,
  setcreateMultiPaymentReducer,
  setcreateMultiPaymentStatusReducer,
  setValidateMultiOrderPaymentReducer,
  setValidateMultiOrderPaymentStatusReducer,
  setsubmitMultiOrderPaymentReducer,
  setsubmitMultiOrderPaymentStatusReducer,
  setInteractFailureReducer
} from "./Reducers";
import { CCDetails, CCDetailsDefault, IBankInfoRes, IGetRedirectUrl, IBankInfoReqPayload, IBankInfoFailure } from "../models";
import { ValidationErrors } from "../models/Error";
import { EpicRoot } from "./EpicRoot";

const { BaseStore, actionsToComputedPropertyName } = CommonFeatures;
// import { CreditCardInfo } from "../models/CreditCardInfo";


const { setConfig, getConfig,
  onCreditCardNumberChange,
  onCardHolderNameChange,
  onCreditCardExpiryDateChange,
  onSecurityCodeChange,
  setValidationErrors,
  resetValidationErrors,
  createPaymentAction,
  createPaymentCompleted,
  createPaymentFailed,
  validateOrderPaymentAction,
  validateOrderPaymentActionCompleted,
  validateOrderPaymentActionFailed,
  submitOrderPaymentAction,
  submitOrderPaymentActionCompleted,
  submitOrderPaymentActionFailed,
  createMultiPaymentAction,
  createMultiPaymentCompleted,
  createMultiPaymentFailed,
  validateMultiOrderPaymentAction,
  validateMultiOrderPaymentActionCompleted,
  validateMultiOrderPaymentActionFailed,
  submitMultiOrderPaymentAction,
  submitMultiOrderPaymentActionCompleted,
  submitMultiOrderPaymentActionFailed,
  // setCreditCardInfo,
  // clearCardNumber,
  setPassKey,
  cardTokenizationError,
  cardTokenizationSuccess,
  tokenizeAndPropagateFormValues,
  redirectUrlSuccess,
  interacBankInfoSuccess,
  setIsLoading,
  interacBankInfoFailure
} = actionsToComputedPropertyName(actions);


@Injectable
export default class Store extends BaseStore<State> {
  constructor(store: BwtkStore, private config: Config, private localization: Localization, private epics: EpicRoot) { super(store); }

  get reducer(): Reducer<State> {
    return combineReducers<State>({
      localization: this.localization.createReducer() as any,
      // widgetState: handleActions({
      //   [widgetStatus]: (store, { payload: state }) => state
      // }, WidgetStates.INITIALIZED),

      config: handleActions<Config>(
        {
          [setConfig]: setConfigReducer,
          [getConfig]: getConfigReducer
        },
        this.config
      ),
      creditCardDetails: handleActions<CCDetails>(
        {
          [onCreditCardNumberChange]: onCreditCardNumberChangeReducer,
          [onCardHolderNameChange]: onCardHolderNameChangeReducer,
          [onSecurityCodeChange]: onSecurityCodeChangeReducer,
          [onCreditCardExpiryDateChange]: onCreditCardExpiryDateChangeReducer,
        },
        CCDetailsDefault
      ),
      validationErrors: handleActions<ValidationErrors>(
        {
          [setValidationErrors]: setValidationErrorsReducer,
          [resetValidationErrors]: setValidationErrorsReducer,
        },
        { errors: [] }
      ),
      createPaymentStatus: handleActions<IRequestStatus, IRequestStatus>({
        [createPaymentAction]: setcreatePaymentStatusReducer(IRequestStatus.PENDING),
        [createPaymentFailed]: setcreatePaymentStatusReducer(IRequestStatus.FAILED),
        [createPaymentCompleted]: setcreatePaymentStatusReducer(IRequestStatus.COMPLETED)
      }, IRequestStatus.IDLE),
      createPayment: handleActions<any, any>({
        [createPaymentCompleted]: setcreatePaymentReducer()
      }, {}
      ),
      validateOrderFormStatus: handleActions<IRequestStatus, IRequestStatus>({
        [validateOrderPaymentAction]: setValidateOrderPaymentStatusReducer(IRequestStatus.PENDING),
        [validateOrderPaymentActionFailed]: setValidateOrderPaymentStatusReducer(IRequestStatus.FAILED),
        [validateOrderPaymentActionCompleted]: setValidateOrderPaymentStatusReducer(IRequestStatus.COMPLETED)
      }, IRequestStatus.IDLE),
      validateOrderPayment: handleActions<any, any>({
        [validateOrderPaymentActionCompleted]: setValidateOrderPaymentReducer()
      }, {}
      ),
      submitOrderFormStatus: handleActions<IRequestStatus, IRequestStatus>({
        [submitOrderPaymentAction]: setsubmitOrderPaymentStatusReducer(IRequestStatus.PENDING),
        [submitOrderPaymentActionFailed]: setsubmitOrderPaymentStatusReducer(IRequestStatus.FAILED),
        [submitOrderPaymentActionCompleted]: setsubmitOrderPaymentStatusReducer(IRequestStatus.COMPLETED)
      }, IRequestStatus.IDLE),
      submitOrderPayment: handleActions<any, any>({
        [submitOrderPaymentActionCompleted]: setsubmitOrderPaymentReducer()
      }, {}
      ),

      createMultiPaymentStatus: handleActions<IRequestStatus, IRequestStatus>({
        [createMultiPaymentAction]: setcreateMultiPaymentStatusReducer(IRequestStatus.PENDING),
        [createMultiPaymentFailed]: setcreateMultiPaymentStatusReducer(IRequestStatus.FAILED),
        [createMultiPaymentCompleted]: setcreateMultiPaymentStatusReducer(IRequestStatus.COMPLETED)
      }, IRequestStatus.IDLE),
      createMultiPayment: handleActions<any, any>({
        [createMultiPaymentCompleted]: setcreateMultiPaymentReducer()
      }, {}
      ),
      validateMultiOrderFormStatus: handleActions<IRequestStatus, IRequestStatus>({
        [validateMultiOrderPaymentAction]: setValidateMultiOrderPaymentStatusReducer(IRequestStatus.PENDING),
        [validateMultiOrderPaymentActionFailed]: setValidateMultiOrderPaymentStatusReducer(IRequestStatus.FAILED),
        [validateMultiOrderPaymentActionCompleted]: setValidateMultiOrderPaymentStatusReducer(IRequestStatus.COMPLETED)
      }, IRequestStatus.IDLE),
      validateMultiOrderPayment: handleActions<any, any>({
        [validateOrderPaymentActionCompleted]: setValidateMultiOrderPaymentReducer()
      }, {}
      ),
      submitMultiOrderFormStatus: handleActions<IRequestStatus, IRequestStatus>({
        [submitMultiOrderPaymentAction]: setsubmitMultiOrderPaymentStatusReducer(IRequestStatus.PENDING),
        [submitMultiOrderPaymentActionFailed]: setsubmitMultiOrderPaymentStatusReducer(IRequestStatus.FAILED),
        [submitMultiOrderPaymentActionCompleted]: setsubmitMultiOrderPaymentStatusReducer(IRequestStatus.COMPLETED)
      }, IRequestStatus.IDLE),
      submitMultiOrderPayment: handleActions<any, any>({
        [submitMultiOrderPaymentActionCompleted]: setsubmitMultiOrderPaymentReducer()
      }, {}
      ),
      passKey: handleActions({
        [setPassKey]: setPasskeyReducer()
      }, ""),
      cardTokenizationError: handleActions<string, string>({
        [cardTokenizationError]: (_state: string, { payload: data }: { payload: string }) => data
      }, ""),
      cardTokenizationSuccess: handleActions<string, string>({
        [cardTokenizationSuccess]: (_state: string, { payload: data }: { payload: string }) => data
      }, ""),
      tokenizeAndPropagateFormValuesStatus: handleActions<IRequestStatus, IRequestStatus>({
        [tokenizeAndPropagateFormValues]: setTokenizeAndPropagateFormValuesStatusReducer(IRequestStatus.PENDING),
        [cardTokenizationError]: setTokenizeAndPropagateFormValuesStatusReducer(IRequestStatus.FAILED),
        [cardTokenizationSuccess]: setTokenizeAndPropagateFormValuesStatusReducer(IRequestStatus.COMPLETED),
      }, IRequestStatus.IDLE),
      redirectUrl: handleActions<IGetRedirectUrl, IGetRedirectUrl>({
        [redirectUrlSuccess]: setGetRedirectUrlReducer()
      }, { status: "", externalRedirectUrl: "" }
      ),
      interacBankInfo: handleActions<IBankInfoReqPayload, IBankInfoRes>({
        [interacBankInfoSuccess]: setInteracBankInfoReducer()
      }, { status: "", bankAccountNumber: "", transitNumber: "", bankCode: "", accountHolderName: "" }
      ),
      interactBankFailureInfo: handleActions({
        [interacBankInfoFailure]: setInteractFailureReducer()
      }, {}),

      isLoading: handleActions<boolean>({
        [setIsLoading]: setIsLoadingReducer
      }, false),
    });
  }

  get middlewares(): any {
    const epicMiddleware = createEpicMiddleware();
    // Run the root epic after a short delay to ensure store is initialized
    setTimeout(() => {
      epicMiddleware.run(this.epics.combineEpics());
    }, 0);
    return [epicMiddleware];
  }
}

export interface State {
  // widgetState: WidgetStates;
  localization: LocalizationState;
  [key: string]: unknown;
  config: Config;
  creditCardDetails: CCDetails;
  validationErrors: ValidationErrors;
  submitOrderPayment: any;
  submitMultiOrderPayment: any;
  submitMultiOrderFormStatus: IRequestStatus;
  validateMultiOrderFormStatus: IRequestStatus;
  // creditCardInfo: CreditCardInfo;
  cardTokenizationError: string;
  passKey: string;
  cardTokenizationSuccess: string;
  tokenizeAndPropagateFormValuesStatus: IRequestStatus;
  redirectUrl: IGetRedirectUrl;
  interacBankInfo: IBankInfoRes;
  isLoading: boolean;
  interactBankFailureInfo: IBankInfoFailure;
}
export interface IStoreState {
  localization: LocalizationState;
  config: Config;
  createPaymentData: any;
  createPayment: any;
  isLoading: boolean;
}
