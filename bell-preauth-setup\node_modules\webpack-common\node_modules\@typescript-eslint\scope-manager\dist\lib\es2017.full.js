"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2017_full = void 0;
const dom_1 = require("./dom");
const dom_iterable_1 = require("./dom.iterable");
const es2017_1 = require("./es2017");
const scripthost_1 = require("./scripthost");
const webworker_importscripts_1 = require("./webworker.importscripts");
exports.es2017_full = {
    ...es2017_1.es2017,
    ...dom_1.dom,
    ...webworker_importscripts_1.webworker_importscripts,
    ...scripthost_1.scripthost,
    ...dom_iterable_1.dom_iterable,
};
//# sourceMappingURL=es2017.full.js.map